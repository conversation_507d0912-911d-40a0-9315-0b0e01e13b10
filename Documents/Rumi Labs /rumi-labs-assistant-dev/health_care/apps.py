from django.apps import AppConfig
from django.db.models.signals import post_migrate

class HealthCareConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'health_care'

    def ready(self):
        from .management.populate_data import populate_clinic_notes_templates,populate_output_styles,populate_clinic_specialities
        post_migrate.connect(populate_clinic_notes_templates, sender=self)
        post_migrate.connect(populate_output_styles, sender=self)
        post_migrate.connect(populate_clinic_specialities,sender=self)