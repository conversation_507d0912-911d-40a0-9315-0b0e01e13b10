from typing import Dict, Any
import json
from rest_framework import serializers

from accounts.serializers import ClinicUserSerializer
from .models import (
    ClinicalNotesDefaultTemplate,
    ClinicalNotesTemplate,
    Patient,
    TemplateFields,
    Transaction,
    Speciality,
    SubSpeciality,
    DoctorInformation,
    OutputStyle,
)

class BaseTransactionSerializer(serializers.ModelSerializer):
    """Base serializer for Transaction model with common functionality."""
    
    def to_representation(self, instance) -> Dict[str, Any]:
        """Convert transcript field from JSON string to dictionary."""
        representation = super().to_representation(instance)
        
        if instance.transcript:
            try:
                representation['transcript'] = json.loads(instance.transcript)
            except json.JSONDecodeError:
                representation['transcript'] = {}
        return representation

class TransactionSerializer(serializers.ModelSerializer):
    """Serializer for creating and updating Transaction instances."""
    
    class Meta:
        model = Transaction
        fields = [
            "id",
            "patient",
            "diagnosis",
            "type",
            "audio_file_meta",
            "uploaded_by",
            "uploaded_at",
            "submission_status",
            "patient_data"
        ]

class GetTransactionSerializer(BaseTransactionSerializer):
    """Serializer for retrieving all Transaction fields."""
    
    class Meta:
        model = Transaction
        fields = "__all__"

class TemplateFieldsSerializer(serializers.ModelSerializer):
    """Serializer for Template Fields."""
    
    class Meta:
        model = TemplateFields
        fields = "__all__"

class ClinicalNotesTemplateSerializer(serializers.ModelSerializer):
    """Serializer for Clinical Notes Template with nested fields."""
    
    fields = TemplateFieldsSerializer(many=True, required=False)

    class Meta:
        model = ClinicalNotesTemplate
        fields = ["id", "name", "fields"]
        depth = 2

class ClinicalNotesDefaultTemplateSerializer(serializers.ModelSerializer):
    """Serializer for Default Clinical Notes Template."""
    
    class Meta:
        model = ClinicalNotesDefaultTemplate
        fields = "__all__"
        depth = 2

class SpecialitySerializer(serializers.ModelSerializer):
    """Serializer for medical specialities."""
    
    class Meta:
        model = Speciality
        fields = "__all__"

class BaseSubSpecialitySerializer(serializers.ModelSerializer):
    """Base serializer for medical subspecialities."""
    
    class Meta:
        model = SubSpeciality
        fields = "__all__"

class SubSpecialitySerializer(BaseSubSpecialitySerializer):
    """Serializer for individual subspeciality operations."""
    pass

class SubSpecialityListSerializer(BaseSubSpecialitySerializer):
    """Serializer for listing subspecialities."""
    pass

class DoctorInformationSerializer(serializers.ModelSerializer):
    """Base serializer for doctor information."""
    
    class Meta:
        model = DoctorInformation
        fields = "__all__"

class DoctorListInformationSerializer(serializers.ModelSerializer):
    """Serializer for listing doctor information with nested user details."""
    
    doctor = ClinicUserSerializer()

    class Meta:
        model = DoctorInformation
        fields = "__all__"

class OutputStyleSerializer(serializers.ModelSerializer):
    """Serializer for output style preferences."""
    
    class Meta:
        model = OutputStyle
        fields = "__all__"

class PatientSerializer(serializers.ModelSerializer):
    """Serializer for patient information."""
    
    class Meta:
        model = Patient
        fields = '__all__'