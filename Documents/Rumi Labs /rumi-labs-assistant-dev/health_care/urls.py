from django.urls import path
from .views import GenerateClinicNotes, ListDefaultClinicalNotesTemplate, PatientListCreateView, \
    PatientRetrieveUpdateDestroyView, RetrieveUpdateDeleteClinicalNotesTemplate, \
    ListCreateClinicalNotesTemplate, CreateListDoctorInfoView, RetrieveUpdateDeleteDoctorInfoView, CreateSpecialityView, \
    ListSpecialityView, RetrieveSpecialityView, UpdateDeleteSpecialityView, CreateSubSpecialityView, \
    ListSubSpecialityView, RetrieveSubSpecialityView, UpdateDeleteSubSpecialityView, ListOutputStyleView, \
    RetrieveUpdateDeleteOutputStyleView, ListTransactionView, RetrieveUpdateDeleteTransactionView, \
    RetrievePatientTransactionsView, GenerateClinicNotesV2, GeneratePreSigned, PatientRetrieveViaEmailView

app_name = 'health_care'

urlpatterns = [

    path('patients/', PatientListCreateView.as_view(), name='create-list-patients'),
    path('patient/<int:pk>/', PatientRetrieveUpdateDestroyView.as_view(), name='retrieve-update-delete-patients'),
    path('patient/<str:email>/', PatientRetrieveViaEmailView.as_view(), name='retrieve-patients'),
    path('doctor_infos/', CreateListDoctorInfoView.as_view(), name='create-list-doctors'),
    path('doctor_info/<int:doctor>/', RetrieveUpdateDeleteDoctorInfoView.as_view(), name='retrieve-update-delete-doctors'),
    path('add_speciality/', CreateSpecialityView.as_view(), name='add-speciality'),
    path('get_speciality/<int:pk>/', RetrieveSpecialityView.as_view(), name='get-speciality'),
    path('speciality/<int:pk>/', UpdateDeleteSpecialityView.as_view(), name='update-delete-speciality'),
    path('add_sub_speciality/', CreateSubSpecialityView.as_view(), name='add-sub-speciality'),
    path('list_speciality/', ListSpecialityView.as_view(), name='list-speciality'),
    path('list_sub_speciality/<int:speciality>/', ListSubSpecialityView.as_view(), name='list-sub-speciality'),
    path('get_sub_speciality/<int:pk>/', RetrieveSubSpecialityView.as_view(), name='get_sub-speciality'),
    path('sub_speciality/<int:pk>/', UpdateDeleteSubSpecialityView.as_view(), name='update-delete-speciality'),
    path('output_styles/', ListOutputStyleView.as_view(), name='update-delete-speciality'),
    path('output_style/<int:pk>/', RetrieveUpdateDeleteOutputStyleView.as_view(), name='update-delete-speciality'),

    path('generate_notes/', GenerateClinicNotes.as_view(), name='generate-notes'),
    path('v2/generate_notes/', GenerateClinicNotesV2.as_view(), name='generate-notes-v2'),
    path('transactions/', ListTransactionView.as_view(), name='list-transactions'),
    path('transaction/<int:pk>/', RetrieveUpdateDeleteTransactionView.as_view(), name='rud-transactions'),
    path('patient_transactions/<int:patient>/', RetrievePatientTransactionsView.as_view(), name='list-patient-transactions'),
    path('generate_post_url/', GeneratePreSigned.as_view(), name='post-presigned-url'),
    path('default-clinic-templates/', ListDefaultClinicalNotesTemplate.as_view(), name='generate-notes'),
    path('clinic-templates/', ListCreateClinicalNotesTemplate.as_view(), name='notes-template-create-list'),
    path('clinic-template/<str:name>/', RetrieveUpdateDeleteClinicalNotesTemplate.as_view(), name='rud-templates'),

]