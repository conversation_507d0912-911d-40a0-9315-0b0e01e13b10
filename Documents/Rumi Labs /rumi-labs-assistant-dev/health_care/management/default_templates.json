[{"name": "SOAP Notes", "template_fields": [{"name": "Subjective", "description": "Patient’s reported symptoms, history, and concerns (e.g., “I have a headache”)."}, {"name": "Objective", "description": "Measurable findings from exams, labs, or imaging (e.g., “Blood pressure: 120/80”)."}, {"name": "Assessment", "description": "Diagnosis or differential diagnoses based on findings (e.g., “Migraine vs tension headache”)."}, {"name": "Plan", "description": "Treatment and next steps (e.g., “Prescribe medication, follow-up in 2 weeks”)."}]}, {"name": "PIE Notes", "template_fields": [{"name": "Problem", "description": "Statement of the patient issue (e.g., “Chest pain”)."}, {"name": "Intervention", "description": "Actions taken to address the problem (e.g., “Administered nitroglycerin”)."}, {"name": "Evaluation", "description": "Diagnosis or differential diagnoses based on findings (e.g., “Migraine vs tension headache”)."}]}, {"name": "CHART Notes", "template_fields": [{"name": "Chief <PERSON><PERSON><PERSON><PERSON>", "description": "Primary issue or reason for visit (e.g., “Shortness of breath”)."}, {"name": "History", "description": "Medical, surgical, family, and social history."}, {"name": "Assessment", "description": "Findings and diagnosis (e.g., “Asthma exacerbation”)."}, {"name": "Rx/Treatment", "description": "Treatment plan or medications (e.g., “Inhaler prescribed”)."}]}, {"name": "SBAR for Communication", "template_fields": [{"name": "Situation", "description": "Current issue (e.g., “Patient has a sudden drop in blood pressure”)."}, {"name": "Background", "description": "Relevant medical history or context."}, {"name": "Assessment", "description": "Observations or concerns (e.g., “Suspected internal bleeding”)."}, {"name": "Recommendation", "description": "Proposed actions (e.g., “Order stat imaging and fluids”)."}]}, {"name": "DAR Notes", "template_fields": [{"name": "Data", "description": "Patient information or observations (e.g., “Patient reports nausea”)."}, {"name": "Action", "description": "Steps taken (e.g., “Administered antiemetic”)."}, {"name": "Response", "description": "Patient’s reaction to action (e.g., “Nausea resolved within 30 minutes”)."}]}, {"name": "H&P (History and Physical)", "template_fields": [{"name": "History", "description": "Includes Chief <PERSON><PERSON><PERSON><PERSON> (CC), History of Present Illness (HPI), Past Medical History (PMH), Family History (FH), Social History (SH), and Review of Systems (ROS)."}, {"name": "Physical Examination", "description": "Findings from the physical exam."}]}, {"name": "POMR (Problem-Oriented Medical Record)", "template_fields": [{"name": "Problem List", "description": "Comprehensive list of all patient issues."}, {"name": "Database", "description": "Patient’s history, exams, labs, and imaging"}, {"name": "Plan", "description": "Specific plans for each problem (e.g., “Monitor glucose levels for diabetes”)."}]}, {"name": "Narrative Notes", "template_fields": [{"name": "Free Textual Notes", "description": "Free-text, descriptive notes to elaborate on patient care, often used in less formal settings or as supplementary documentation."}]}, {"name": "Operative Note Template", "template_fields": [{"name": "Preoperative Diagnosis", "description": "Reason for surgery."}, {"name": "Postoperative Diagnosis", "description": "Findings after surgery."}, {"name": "Procedure Performed", "description": "Description of the surgery."}, {"name": "Findings", "description": "Intraoperative observations."}, {"name": "Complications", "description": "Any issues encountered."}, {"name": "Plan", "description": "Postoperative care instructions."}]}]