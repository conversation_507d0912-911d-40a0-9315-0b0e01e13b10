import json
from health_care.models import ClinicalNotesDefaultTemplate, ClinicalNotesTemplate, TemplateFields, OutputStyle, \
    Speciality, SubSpeciality


def populate_clinic_specialities(sender, **kwargs):
    with open("health_care/management/specialities.json", "r", encoding="utf-8") as f:
        specialites = json.load(f)
    for speciality in specialites:
        speciality_obj,_ = Speciality.objects.get_or_create(name = speciality["name"],description=speciality["description"])
        for subspecialty in speciality["subspecialties"]:
            field, _ = SubSpeciality.objects.get_or_create(name=subspecialty["name"],description=subspecialty["description"],speciality=speciality_obj)
    print("Specialities Populated in DB")


def populate_clinic_notes_templates(sender, **kwargs):
    with open("health_care/management/default_templates.json", "r", encoding="utf-8") as f:
        templates = json.load(f)
    for template_data in templates:
        if not ClinicalNotesDefaultTemplate.objects.filter(name=template_data["name"]).exists():
            fields_data = template_data.get("template_fields")
            template = ClinicalNotesDefaultTemplate.objects.create(name = template_data["name"])
            for field_data in fields_data:
                field, created = TemplateFields.objects.get_or_create(**field_data)
                if created:
                    template.fields.add(field)
    print("Templates Populated in DB")

def populate_output_styles(sender,**kwargs):
    try:

        with open("health_care/management/output_styles.json", "r", encoding="utf-8") as f:
            styles = json.load(f)
        initial_data = []
        for style in styles:
            if not OutputStyle.objects.filter(name=style["name"]).exists():
                initial_data.append(OutputStyle(name=style["name"],description = style["description"],example=style["example"]))

        OutputStyle.objects.bulk_create(initial_data)
        print("Output styles populated in DB")
    except Exception as e:
        print("Failed Populating Templates",e)
