import json
import logging
from typing import <PERSON><PERSON>, <PERSON><PERSON>, Dict, Any, List

from django.db import transaction
from botocore.exceptions import BotoCoreError, ClientError

from accounts.models import Organization
from deep_models.logics import load_template_on_llm
from deep_models.transcribing_models.AssemblyAI import AssemblyAI
from deep_models.transcribing_models.DeepGram import DeepGram
from health_care.models import (
    ClinicalNotesTemplate,
    Template<PERSON><PERSON>s,
    DoctorInformation,
    Patient,
    ClinicalNotesDefaultTemplate, Transaction
)
from project_helper.aws_helper import S3Helper

logger = logging.getLogger(__name__)

class ClinicalNotesError(Exception):
    """Base exception for clinical notes operations."""
    pass

class AudioTranscriptionError(ClinicalNotesError):
    """Raised when audio transcription fails."""
    pass

class S3UploadError(ClinicalNotesError):
    """Raised when S3 upload fails."""
    pass

class FileHandler:


    @staticmethod
    def upload_file_to_s3(user: Any, file: Any, transaction_obj: Any) -> None:
        """
        Upload file to S3 bucket and update transaction object with file path.
        
        Args:
            user: User object containing organization details
            file: File object to upload
            transaction_obj: Transaction object to update with file path
        
        Raises:
            S3UploadError: If file upload fails
        """
        if not file:
            return
        s3_helper = S3Helper()
        organization = user.organization
        audio_path = f'{user.id}/{transaction_obj.id}/{file.name}'

        try:
            response = s3_helper.upload_file_obj_to_bucket(
                file, 
                organization.bucket_name, 
                audio_path
            )
            if not response:
                raise S3UploadError("File upload failed")
            
            transaction_obj.audio_file_meta["path"] = audio_path
            transaction_obj.save()

        except (BotoCoreError, ClientError) as e:
            logger.error(f"S3 upload failed: {str(e)}")
            raise S3UploadError(f"S3 upload failed: {str(e)}")

class TransactionManager:
    @staticmethod
    @transaction.atomic
    def save_transaction(
        user_info: Any,
        transcript: Dict,
        notes: str,
        audio_duration,
        patient_data: Dict,
        trn_obj: Transaction,
    ) -> Any:
        """
        Save transaction details with atomic transaction.
        
        Args:
            user_info: User information
            transcript: Transcript dictionary
            notes: Clinical notes
            patient_id: Patient ID
            trn_obj: Transaction object
        
        Returns:
            Updated transaction object
        """
        try:
            print("Transaction",trn_obj.id)
            # patient = Patient.objects.filter(id=patient_id).first()
            
            trn_obj.clinic_notes = notes
            trn_obj.transcript = json.dumps(transcript, indent=4)
            trn_obj.uploaded_by = user_info
            trn_obj.audio_file_meta["duration"] = audio_duration
            if patient_data:
                trn_obj.patient_data = json.loads(patient_data)
            
            trn_obj.save()
            return trn_obj

        except Exception as e:
            print("Exception while saving transaction",e)
            logger.error(f"Error saving transaction: {str(e)}")
            raise e

class TranscriptionService:
    @staticmethod
    def create_clinical_notes(
        audio_file: Any,
        user: Any,
        template_id: Optional[int],
        style: str
    ) -> Tuple[List[Dict], str, str]:
        """
        Create clinical notes from audio file.
        
        Args:
            audio_file: Audio file to transcribe
            user: User object
            template_id: Optional template ID
        
        Returns:
            Tuple containing (transcript_dict, transcript_text, clinical_notes)
        """
        try:
            deepgram = DeepGram()
            transcript, transcript_text = deepgram.transcribe(audio_file)
        except Exception as e:
            logger.error(f"DeepGram transcription failed: {str(e)}")
            raise e
            # try:
            #     assembly_ai = AssemblyAI()
            #     transcript, transcript_text = assembly_ai.transcribe(audio_file)
            # except Exception as e:
            #     logger.error(f"AssemblyAI transcription failed: {str(e)}")
            #     raise AudioTranscriptionError("All transcription services failed")

        if not transcript_text:
            raise AudioTranscriptionError("No transcript generated")

        clinic_notes = NotesGenerator.generate_notes(transcript_text, user, template_id,style)
        return transcript, transcript_text, clinic_notes

class NotesGenerator:
    @staticmethod
    def generate_notes(
        transcript: str,
        user: Any,
        template_id: Optional[int] = None,
        style: str = None
    ) -> str:
        """
        Generate clinical notes from transcript.
        
        Args:
            transcript: Transcript text
            user: User object
            template_id: Optional template ID
        
        Returns:
            Generated clinical notes
        """
        doctor_info = DoctorInformation.objects.filter(doctor=user).first()
        # template = TemplateManager.get_template(doctor_info, template_id)
        # fields = TemplateManager.format_template_fields(template)

        style = doctor_info.output_style.name if not style else style
        if not style:
            style = "paragraph"
        params = {
            "format": style,
            "transcript": transcript,
        }
        
        return load_template_on_llm("GENERATE_CLINICAL_NOTES", params)

class TemplateManager:
    @staticmethod
    def get_template(
        doctor_info: Any,
        template_id: Optional[int]
    ) -> ClinicalNotesTemplate:
        """Get appropriate template based on priority."""
        if template_id:
            template = ClinicalNotesTemplate.objects.filter(
                organization=doctor_info.doctor.organization,
                id=template_id
            ).first()
            if template:
                return template
        
        if doctor_info.selected_template:
            return doctor_info.selected_template
        
        return ClinicalNotesDefaultTemplate.objects.first()


    def add_default_templates_to_organization(self, organization: Organization):
        """Add default templates to an organization."""
        default_templates = ClinicalNotesDefaultTemplate.objects.all()
        for template in default_templates:
            template_obj, _ = ClinicalNotesTemplate.objects.get_or_create(
                name=template.name,
                organization=organization
            )
            template_obj.fields.set(template.fields.all())
            template_obj.save()

    @staticmethod
    def format_template_fields(template: ClinicalNotesTemplate) -> str:
        """Format template fields into string."""
        return "\n".join(
            f"{i}) {field.name} : {field.description}"
            for i, field in enumerate(template.fields.all(), 1)
        )

    @staticmethod
    @transaction.atomic
    def create_template_with_fields(data: Dict) -> ClinicalNotesTemplate:
        """Create template with fields in atomic transaction."""
        template = ClinicalNotesTemplate.objects.create(
            name=data.get("name"),
            organization=data.get("organization")
        )
        
        for field_data in data.get('fields', []):
            field = TemplateFields.objects.create(
                name=field_data["name"],
                description=field_data["description"]
            )
            template.fields.add(field)
        
        return template

def increment_api_count(organization):
    organization.apis_count += 1
    organization.save()