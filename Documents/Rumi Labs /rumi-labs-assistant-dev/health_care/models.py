from django.db import models
from accounts.models import Organization, ClinicUser

class OutputStyle(models.Model):
    id = models.AutoField(primary_key=True, db_column="id", auto_created=True)
    name = models.CharField(blank=True, max_length=200)
    description = models.CharField(blank=True, max_length=2000)
    example = models.TextField(blank=True)

class Speciality(models.Model):
    id = models.AutoField(primary_key=True, db_column="id", auto_created=True)
    name = models.CharField(blank=True, max_length=200)
    description = models.CharField(blank=True, max_length=2000)

class SubSpeciality(models.Model):
    id = models.AutoField(primary_key=True, db_column="id", auto_created=True)
    name = models.CharField(blank=True, max_length=200)
    description = models.CharField(blank=True, max_length=2000)
    speciality = models.ForeignKey(Speciality,on_delete=models.CASCADE)


class Patient(models.Model):
    full_name = models.Char<PERSON>ield(max_length=255, blank=True)
    phone_number = models.CharField(max_length=15, blank=True)
    age = models.IntegerField(null=True, blank=True)
    email = models.EmailField(null=True, blank=True)
    gender = models.CharField(max_length=10,blank=True)
    address = models.TextField(blank=True)
    added_by = models.ForeignKey(ClinicUser,on_delete=models.SET_NULL,null=True)
    def __str__(self):
        return self.full_name

STATUS_CHOICES = [
    ("PENDING", 'PENDING'),
    ("IN_PROGRESS", 'IN_PROGRESS'),
    ("COMPLETED", 'COMPLETED'),
]
class Transaction(models.Model):
    id = models.AutoField(primary_key=True, db_column="id", auto_created=True)
    patient = models.ForeignKey(Patient,null=True, blank=True,on_delete=models.SET_NULL)
    diagnosis = models.CharField(blank=True, max_length=200)
    type = models.CharField(blank=True, max_length=200)
    audio_file_meta = models.JSONField(blank=True, null=True, max_length=5000,default={})
    patient_data = models.JSONField(blank=True, null=True, max_length=10000,default={})
    transcript = models.TextField(blank=True)
    clinic_notes = models.TextField(blank=True)
    uploaded_by = models.ForeignKey("accounts.ClinicUser",on_delete=models.SET_NULL,null=True)
    uploaded_at = models.DateTimeField(auto_now=True)
    submission_status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default="PENDING",
    )
    api_time = models.FloatField(default=0.0,blank=True)

    def __str__(self):
        return f"{self.patient.name}_{self.uploaded_by}"


class TemplateFields(models.Model):
    id = models.AutoField(primary_key=True, db_column="id", auto_created=True)
    name = models.CharField(max_length=500, blank=False)
    description = models.CharField(max_length=5000, blank=False)


class ClinicalNotesDefaultTemplate(models.Model):
    id = models.AutoField(primary_key=True, db_column="id", auto_created=True)
    name = models.CharField(max_length=500, blank=True, unique=True)
    fields = models.ManyToManyField(TemplateFields, blank=True, null=True)


class ClinicalNotesTemplate(models.Model):
    id = models.AutoField(primary_key=True, db_column="id", auto_created=True)
    organization = models.ForeignKey(Organization, related_name="organization", db_column="organization",
                                     on_delete=models.CASCADE)
    name = models.CharField(max_length=500, blank=True)
    fields = models.ManyToManyField(TemplateFields, blank=True, null=True)

    class Meta:
        unique_together = (("name", "organization"),)


class DoctorInformation(models.Model):
    doctor = models.ForeignKey(ClinicUser, on_delete=models.SET_NULL, null=True,unique=True)
    speciality = models.ForeignKey(Speciality,on_delete=models.SET_NULL,null=True)
    sub_speciality = models.ForeignKey(SubSpeciality,on_delete=models.SET_NULL,null=True)
    output_style = models.ForeignKey(OutputStyle,on_delete=models.SET_NULL,null=True)
    selected_template = models.ForeignKey(ClinicalNotesTemplate,null=True, on_delete=models.SET_NULL)