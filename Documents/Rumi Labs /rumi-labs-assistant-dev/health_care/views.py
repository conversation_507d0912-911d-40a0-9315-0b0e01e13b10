import time

from django.core.exceptions import ObjectDoesNotExist
from rest_framework.permissions import IsAuthenticated

from accounts.permissions import IsAdminOnly, IsDoctorOnly, \
    IsSuperUserOnly
from health_care.logics import <PERSON>mp<PERSON><PERSON><PERSON><PERSON>, FileHandler, TransactionManager, TranscriptionService, NotesGenerator, \
    increment_api_count
from rest_framework import generics, status, filters
from django.db.models import Q

from health_care.models import ClinicalNotesDefaultTemplate, ClinicalNotesTemplate, DoctorInformation, Patient, \
    Speciality, OutputStyle, SubSpeciality
from health_care.serializers import ClinicalNotesTemplateSerializer, ClinicalNotesDefaultTemplateSerializer, \
    DoctorInformationSerializer, SpecialitySerializer, SubSpecialitySerializer, OutputStyleSerializer, \
    SubSpecialityListSerializer, DoctorListInformationSerializer, GetTransactionSerializer
from project_helper.aws_helper import S3Helper
from .serializers import PatientSerializer, TransactionSerializer
from project_helper.pagination_class import TenResultsPagination
from health_care.models import Transaction
from django.utils import timezone
from datetime import timedelta
from project_helper.response_mixins import ResponseMixin
from rest_framework.views import APIView
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
s3_helper = S3Helper()


class CreateListDoctorInfoView(generics.ListCreateAPIView):
    permission_classes = [IsAuthenticated, IsSuperUserOnly | IsAdminOnly | IsDoctorOnly]
    serializer_class = DoctorInformationSerializer
    queryset = DoctorInformation.objects.all()

    def get_queryset(self):
        if not self.request.user.is_authenticated:
            return None
        if self.request.user.is_admin:
            return DoctorInformation.objects.filter(doctor__organization=self.request.user.organization)
        else:
            return DoctorInformation.objects.filter(doctor=self.request.user)


class RetrieveUpdateDeleteDoctorInfoView(generics.RetrieveUpdateDestroyAPIView):
    permission_classes = [IsAuthenticated, IsAdminOnly | IsDoctorOnly]
    serializer_class = DoctorListInformationSerializer
    queryset = DoctorInformation.objects.all()
    lookup_field = "doctor"

    def get_queryset(self):
        if not self.request.user.is_authenticated:
            return None
        if self.request.user.is_superuser:
            return super().get_queryset()
        else:
            return DoctorInformation.objects.filter(doctor__organization=self.request.user.organization)


class CreateSpecialityView(generics.CreateAPIView):
    permission_classes = [IsAuthenticated, IsSuperUserOnly]
    serializer_class = SpecialitySerializer
    queryset = Speciality.objects.all()


class ListSpecialityView(generics.ListAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = SpecialitySerializer
    queryset = Speciality.objects.all()


class RetrieveSpecialityView(generics.ListAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = SpecialitySerializer
    lookup_field = "pk"
    queryset = Speciality.objects.all()


class UpdateDeleteSpecialityView(generics.RetrieveUpdateDestroyAPIView):
    permission_classes = [IsAuthenticated, IsSuperUserOnly]
    serializer_class = SpecialitySerializer
    lookup_field = "pk"
    queryset = Speciality.objects.all()


class CreateSubSpecialityView(generics.CreateAPIView):
    permission_classes = [IsAuthenticated, IsSuperUserOnly]
    serializer_class = SubSpecialitySerializer
    queryset = SubSpeciality.objects.all()


class ListSubSpecialityView(generics.ListAPIView):
    permission_classes = [IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method == "GET":
            return SubSpecialityListSerializer
        else:
            return SubSpecialitySerializer

    def get_queryset(self):
        if not self.request.user.is_authenticated:
            return None
        query = SubSpeciality.objects.all()
        filtered = query.filter(speciality=self.kwargs.get("speciality"))
        return filtered


class RetrieveSubSpecialityView(generics.ListAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = SubSpecialitySerializer
    lookup_field = "pk"
    queryset = SubSpeciality.objects.all()


class UpdateDeleteSubSpecialityView(generics.RetrieveUpdateDestroyAPIView):
    permission_classes = [IsAuthenticated, IsSuperUserOnly]
    serializer_class = SubSpecialitySerializer
    lookup_field = "pk"
    queryset = SubSpeciality.objects.all()


class ListOutputStyleView(generics.ListAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = OutputStyleSerializer
    queryset = OutputStyle.objects.all()


class RetrieveUpdateDeleteOutputStyleView(generics.RetrieveUpdateDestroyAPIView):
    permission_classes = [IsAuthenticated, IsSuperUserOnly]
    serializer_class = OutputStyleSerializer
    queryset = OutputStyle.objects.all()


class GenerateClinicNotes(APIView, ResponseMixin):
    permission_classes = [IsDoctorOnly]

    def post(self, request, *args, **kwargs):
        if not request.user.organization.ready:
            return self.bad_request_response("Organization Bucket or Log Group Unavailable")

        try:
            start = time.time()
            file_obj = request.FILES.get('file')
            patient_data = request.data.get("patient_data")
            template_id = request.data.get("template_id")
            transaction_id = request.data.get("id")
            audio_duration = request.data.get("audio_duration")
            style = request.data.get("style")
            if not file_obj:
                return self.bad_request_response("Invalid Input File")

            trn_obj, _ = Transaction.objects.get_or_create(id=transaction_id)
            trn_obj.submission_status = "IN_PROGRESS"
            trn_obj.save()
            transcript, transcript_text, notes = TranscriptionService.create_clinical_notes(
                file_obj, request.user, template_id,style
            )
            file_obj.seek(0)
            TransactionManager.save_transaction(request.user, transcript, notes,audio_duration, patient_data, trn_obj)
            FileHandler.upload_file_to_s3(request.user, file_obj, trn_obj)

            increment_api_count(request.user.organization)
            trn_obj.submission_status = "PENDING"
            trn_obj.api_time = time.time() - start
            trn_obj.save()
            return self.success_response(
                data={"id": trn_obj.id,"transcript": transcript, "notes": notes,"audio_data":trn_obj.audio_file_meta},
                status_code=status.HTTP_201_CREATED
            )

        except Exception as e:
            return self.internal_server_error_response(str(e))



class GenerateClinicNotesV2(APIView, ResponseMixin):
    permission_classes = [IsDoctorOnly]
    @swagger_auto_schema(
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'file_obj': openapi.Schema(type=openapi.TYPE_STRING, description='File object for the clinical notes'),
                'transcript': openapi.Schema(type=openapi.TYPE_STRING, description='Transcript of the audio'),
                'patient_id': openapi.Schema(type=openapi.TYPE_INTEGER, description='ID of the patient'),
                'template_id': openapi.Schema(type=openapi.TYPE_INTEGER, description='ID of the template to use'),
                'audio_duration': openapi.Schema(type=openapi.TYPE_STRING, description='Duration of the audio'),
            },
            required=[ 'transcript', 'audio_duration']
        ),
        responses={
            201: openapi.Response(
                'Clinical notes generated successfully.',
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'transcript': openapi.Schema(
                            type=openapi.TYPE_ARRAY,
                            items=openapi.Schema(
                                type=openapi.TYPE_OBJECT,
                                properties={
                                    "start": openapi.Schema(type=openapi.FORMAT_FLOAT, description="Start time of the transcript segment"),
                                    "end": openapi.Schema(type=openapi.FORMAT_FLOAT, description="End time of the transcript segment"),
                                    "speaker": openapi.Schema(type=openapi.TYPE_INTEGER, description="Speaker identifier"),
                                    "text": openapi.Schema(type=openapi.TYPE_STRING, description="Text of the transcript segment"),
                                }
                            )
                        ),
                        'notes': openapi.Schema(type=openapi.TYPE_STRING),
                        'audio_data': openapi.Schema(type=openapi.TYPE_OBJECT,
                                                     properties={
                                                         "duration":openapi.Schema(type=openapi.TYPE_STRING),
                                                         "path":openapi.Schema(type=openapi.TYPE_STRING,description="Path of file in S3")
                                                     }),
                    }
                )
            ),
            400: openapi.Response('Invalid input data.'),
            500: openapi.Response('Internal server error.'),
        }
    )
    def post(self, request, *args, **kwargs):
        if not request.user.organization.ready:
            return self.bad_request_response("Organization Bucket or Log Group Unavailable")

        try:
            data = self._validate_input(request.data, request.FILES)
            trn_obj = self._get_or_create_transaction(data['transaction_id'])

            notes = NotesGenerator.generate_notes(
                data['transcript'],
                request.user,
                data['template_id']
            )

            TransactionManager.save_transaction(
                request.user,
                data['file_obj'],
                data['transcript'],
                notes,
                data["audio_duration"],
                data['patient_id'],
                trn_obj
            )

            increment_api_count(request.user.organization)

            return self.success_response(
                data={"transcript": data['transcript'], "notes": notes,"audio_data":trn_obj.audio_file_meta},
                status_code=status.HTTP_201_CREATED
            )

        except ValueError as e:
            return self.error_response(str(e))
        except Exception as e:
            return self.error_response(str(e))

    def _validate_input(self, data, files):
        transcript = data.get('transcript')
        if not transcript:
            raise ValueError("Invalid Transcript")

        return {
            'file_obj': files.get('file'),
            'transcript': transcript,
            'patient_id': data.get("patient_id"),
            'template_id': data.get("template_id"),
            'transaction_id': data.get("transaction_id"),
            'audio_duration': data.get("audio_duration")
        }

    def _get_or_create_transaction(self, transaction_id):
        trn_obj, _ = Transaction.objects.get_or_create(id=transaction_id)
        trn_obj.submission_status = "IN_PROGRESS"
        trn_obj.save()
        return trn_obj


class GeneratePreSigned(APIView, ResponseMixin):
    def post(self, request, *args, **kwargs):
        bucket_name = self._get_bucket_name(request)
        if not bucket_name:
            return self.bad_request_response("Invalid Bucket Name")

        path_to_obj = request.data.get("object")
        try:
            url = s3_helper.create_presigned_url(bucket_name, path_to_obj)
            return self.success_response(
                data={"presigned_url": url},
                status_code=status.HTTP_201_CREATED
            )
        except Exception as e:
            return self.internal_server_error_response(str(e))

    def _get_bucket_name(self, request):
        if request.user.is_superuser:
            return request.body.get("bucket")
        return request.user.organization.bucket_name


class ListTransactionView(generics.ListCreateAPIView):
    permission_classes = [IsAdminOnly | IsDoctorOnly | IsSuperUserOnly]
    serializer_class = TransactionSerializer
    pagination_class = TenResultsPagination
    filter_backends = [filters.OrderingFilter]
    ordering_fields = ['id', 'uploaded_at']

    def get_serializer_class(self):
        if self.request.method != 'GET':
            return GetTransactionSerializer
        else:
            return self.serializer_class

    def get_queryset(self):
        if not self.request.user.is_authenticated:
            return None
        patient_id = self.request.query_params.get('patient')
        diagnosis = self.request.query_params.get('diagnosis')
        uploaded_by = self.request.query_params.get('uploaded_by')
        time_filter = self.request.query_params.get('time_filter')
        order_by = self.request.query_params.get('order_by','id')
        status = self.request.query_params.get('status')

        filters = Q()
        # Admin vs. User level access filtering
        if self.request.user.is_authenticated and self.request.user.is_admin:
            queryset = Transaction.objects.filter(uploaded_by__organization=self.request.user.organization).order_by(order_by)
        else:
            queryset = Transaction.objects.filter(uploaded_by=self.request.user).order_by(order_by)

        if patient_id:
            filters &= Q(patient_id=patient_id)
        if status:
            filters &= Q(submission_status=status)
        if diagnosis:
            filters &= Q(diagnosis__icontains=diagnosis)

        if uploaded_by:
            filters &= Q(uploaded_by_id=uploaded_by)

        # Time-based filtering
        if time_filter:
            now = timezone.now()
            today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
            time_ranges = {
                "today" : today_start,
                "last_day": today_start - timedelta(days=1),
                "last_3_days": today_start - timedelta(days=3),
                "last_7_days": today_start - timedelta(days=7),
                "last_15_days": today_start - timedelta(days=15),
                "last_month": today_start - timedelta(days=30),
            }
            if time_filter in time_ranges:
                filters &= Q(uploaded_at__gte=time_ranges[time_filter])

        return queryset.filter(filters)

    def create(self, request, *args, **kwargs):
        request.data["uploaded_by"] = request.user.id
        response = super().create(request, args, kwargs)
        return response


class RetrievePatientTransactionsView(generics.ListAPIView):
    permission_classes = [IsDoctorOnly | IsAdminOnly | IsSuperUserOnly]
    serializer_class = GetTransactionSerializer
    queryset = Transaction.objects.all()
    lookup_field = "patient"

    def get_queryset(self):
        if not self.request.user.is_authenticated:
            return None
        if self.request.user.is_admin:
            return super().get_queryset().filter(uploaded_by__organization=self.request.user.organization,
                                                 patient_id=self.kwargs.get("patient"))
        else:
            return super().get_queryset().filter(uploaded_by=self.request.user.id,
                                                 patient_id=self.kwargs.get("patient"))


class RetrieveUpdateDeleteTransactionView(generics.RetrieveUpdateDestroyAPIView):
    permission_classes = [IsDoctorOnly | IsAdminOnly | IsSuperUserOnly]
    serializer_class = GetTransactionSerializer
    queryset = Transaction.objects.all()
    lookup_field = "pk"


    def get_queryset(self):
        if not self.request.user.is_authenticated:
            return None
        if self.request.user.is_admin:
            return super().get_queryset().filter(uploaded_by__organization=self.request.user.organization,
                                                 id=self.kwargs.get("pk"))
        else:
            return super().get_queryset().filter(uploaded_by=self.request.user.id, id=self.kwargs.get("pk"))


class ListCreateClinicalNotesTemplate(ResponseMixin,generics.ListCreateAPIView):
    permission_classes = [IsDoctorOnly | IsAdminOnly]
    serializer_class = ClinicalNotesTemplateSerializer

    def get_queryset(self):
        if not self.request.user.is_authenticated:
            return None
        return ClinicalNotesTemplate.objects.filter(organization=self.request.user.organization)

    def create(self, request, *args, **kwargs):
        tempalte_manager = TemplateManager()
        self.request.data["organization"] = self.request.user.organization
        response = tempalte_manager.create_template_with_fields(self.request.data)
        response = self.serializer_class(response)
        return self.success_created_response(response.data)


class ListDefaultClinicalNotesTemplate(generics.ListAPIView):
    permission_classes = [IsSuperUserOnly | IsAdminOnly | IsDoctorOnly]
    queryset = ClinicalNotesDefaultTemplate.objects.all()
    serializer_class = ClinicalNotesDefaultTemplateSerializer


class RetrieveUpdateDeleteClinicalNotesTemplate(generics.RetrieveUpdateDestroyAPIView):
    permission_classes = [IsDoctorOnly | IsAdminOnly | IsSuperUserOnly]
    serializer_class = ClinicalNotesTemplateSerializer
    lookup_field = "name"

    def get_queryset(self):
        if not self.request.user.is_authenticated:
            return None
        if self.request.user.is_superuser:
            return ClinicalNotesTemplate.objects.all()
        return ClinicalNotesTemplate.objects.filter(organization=self.request.user.organization)


class PatientListCreateView(generics.ListCreateAPIView,ResponseMixin):
    permission_classes = [IsAuthenticated, IsDoctorOnly | IsAdminOnly | IsSuperUserOnly]
    queryset = Patient.objects.all()
    serializer_class = PatientSerializer

    def get_queryset(self):
        if not self.request.user.is_authenticated:
            return None
        if self.request.user.is_admin:
            queryset = Patient.objects.filter(added_by__organization=self.request.user.organization)
        else:
            queryset = Patient.objects.filter(added_by=self.request.user)
        full_name = self.request.query_params.get("full_name")
        gender = self.request.query_params.get("gender")
        age = self.request.query_params.get("age")
        email = self.request.query_params.get("email")
        address = self.request.query_params.get("address")
        phone_number = self.request.query_params.get("phone_number")

        filters = Q()  # Initialize an empty Q object

        if full_name:
            filters &= Q(full_name__icontains=full_name)  # AND condition
        if gender:
            filters &= Q(gender=gender)
        if age:
            filters &= Q(age=age)
        if email:
            filters &= Q(email__icontains=email)
        if phone_number:
            filters &= Q(phone_number=phone_number)
        if address:
            filters &= Q(address__icontains=address)

        return queryset.filter(filters)

    def create(self, request, *args, **kwargs):
        if self.request.data.get("email") == self.request.user.email:
            return self.bad_request_response("You can't add yourself as Patient")
        request.data["added_by"] = self.request.user.id
        return super().create(request, args, kwargs)


class PatientRetrieveUpdateDestroyView(generics.RetrieveUpdateDestroyAPIView):
    permission_classes = [IsAuthenticated, IsDoctorOnly | IsAdminOnly | IsSuperUserOnly]
    queryset = Patient.objects.all()
    serializer_class = PatientSerializer
    lookup_field = "pk"


class PatientRetrieveViaEmailView(generics.RetrieveAPIView,ResponseMixin):
    permission_classes = [IsAuthenticated, IsDoctorOnly | IsAdminOnly | IsSuperUserOnly]
    serializer_class = PatientSerializer
    lookup_field = "email"

    def get_queryset(self):
        if not self.request.user.is_authenticated:
            return None
        if self.request.user.is_superuser:
            return Patient.objects.all()
        return Patient.objects.filter(added_by__organization = self.request.user.organization,email=self.kwargs.get("email"))

    def retrieve(self, request, *args, **kwargs):
        if self.request.user.email == self.kwargs.get("email"):
            return self.bad_request_response("You can't add yourself as patient.")

        return super().retrieve(request,args,kwargs)