# Generated by Django 5.0.7 on 2025-02-10 12:51

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounts', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='OutputStyle',
            fields=[
                ('id', models.AutoField(auto_created=True, db_column='id', primary_key=True, serialize=False)),
                ('name', models.CharField(blank=True, max_length=200, null=True)),
                ('description', models.CharField(blank=True, max_length=2000, null=True)),
                ('example', models.TextField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='Speciality',
            fields=[
                ('id', models.AutoField(auto_created=True, db_column='id', primary_key=True, serialize=False)),
                ('name', models.CharField(blank=True, max_length=200, null=True)),
                ('description', models.CharField(blank=True, max_length=2000, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='TemplateFields',
            fields=[
                ('id', models.AutoField(auto_created=True, db_column='id', primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=500)),
                ('description', models.CharField(max_length=5000)),
            ],
        ),
        migrations.CreateModel(
            name='ClinicalNotesTemplate',
            fields=[
                ('id', models.AutoField(auto_created=True, db_column='id', primary_key=True, serialize=False)),
                ('name', models.CharField(blank=True, max_length=500, null=True)),
                ('organization', models.ForeignKey(db_column='organization', on_delete=django.db.models.deletion.CASCADE, related_name='organization', to='accounts.organization')),
                ('fields', models.ManyToManyField(blank=True, null=True, to='health_care.templatefields')),
            ],
            options={
                'unique_together': {('name', 'organization')},
            },
        ),
        migrations.CreateModel(
            name='Patient',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('full_name', models.CharField(blank=True, max_length=255, null=True)),
                ('phone_number', models.CharField(blank=True, max_length=15, null=True)),
                ('age', models.IntegerField(blank=True, null=True)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('gender', models.CharField(blank=True, max_length=10, null=True)),
                ('address', models.TextField(blank=True, null=True)),
                ('added_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='SubSpeciality',
            fields=[
                ('id', models.AutoField(auto_created=True, db_column='id', primary_key=True, serialize=False)),
                ('name', models.CharField(blank=True, max_length=200, null=True)),
                ('description', models.CharField(blank=True, max_length=2000, null=True)),
                ('speciality', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='health_care.speciality')),
            ],
        ),
        migrations.CreateModel(
            name='DoctorInformation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('doctor', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, unique=True)),
                ('selected_template', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='health_care.clinicalnotestemplate')),
                ('output_style', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='health_care.outputstyle')),
                ('speciality', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='health_care.speciality')),
                ('sub_speciality', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='health_care.subspeciality')),
            ],
        ),
        migrations.CreateModel(
            name='ClinicalNotesDefaultTemplate',
            fields=[
                ('id', models.AutoField(auto_created=True, db_column='id', primary_key=True, serialize=False)),
                ('name', models.CharField(blank=True, max_length=500, null=True, unique=True)),
                ('fields', models.ManyToManyField(blank=True, null=True, to='health_care.templatefields')),
            ],
        ),
        migrations.CreateModel(
            name='Transaction',
            fields=[
                ('id', models.AutoField(auto_created=True, db_column='id', primary_key=True, serialize=False)),
                ('diagnosis', models.CharField(blank=True, max_length=200, null=True)),
                ('type', models.CharField(blank=True, max_length=200, null=True)),
                ('audio_file_path', models.CharField(blank=True, max_length=1000, null=True)),
                ('transcript', models.TextField(blank=True, null=True)),
                ('clinic_notes', models.TextField(blank=True, null=True)),
                ('uploaded_at', models.DateTimeField(auto_now=True)),
                ('submission_status', models.CharField(choices=[('PENDING', 'PENDING'), ('IN_PROGRESS', 'IN_PROGRESS'), ('COMPLETED', 'COMPLETED')], default='PENDING', max_length=20)),
                ('patient', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='health_care.patient')),
                ('uploaded_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
