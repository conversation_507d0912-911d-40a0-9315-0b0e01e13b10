# Generated by Django 5.0.7 on 2025-03-16 09:59

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('health_care', '0002_remove_transaction_audio_file_path_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='clinicalnotesdefaulttemplate',
            name='name',
            field=models.CharField(blank=True, default='', max_length=500, unique=True),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='clinicalnotestemplate',
            name='name',
            field=models.CharField(blank=True, default='', max_length=500),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='outputstyle',
            name='description',
            field=models.CharField(blank=True, default='', max_length=2000),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='outputstyle',
            name='example',
            field=models.TextField(blank=True, default=''),
            preserve_default=False,
        ),
        migrations.Alter<PERSON>ield(
            model_name='outputstyle',
            name='name',
            field=models.CharField(blank=True, default='', max_length=200),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='patient',
            name='address',
            field=models.TextField(blank=True, default=''),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='patient',
            name='full_name',
            field=models.CharField(blank=True, default='', max_length=255),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='patient',
            name='gender',
            field=models.CharField(blank=True, default='', max_length=10),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='patient',
            name='phone_number',
            field=models.CharField(blank=True, default='', max_length=15),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='speciality',
            name='description',
            field=models.CharField(blank=True, default='', max_length=2000),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='speciality',
            name='name',
            field=models.CharField(blank=True, default='', max_length=200),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='subspeciality',
            name='description',
            field=models.CharField(blank=True, default='', max_length=2000),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='subspeciality',
            name='name',
            field=models.CharField(blank=True, default='', max_length=200),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='transaction',
            name='clinic_notes',
            field=models.TextField(blank=True, default=''),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='transaction',
            name='diagnosis',
            field=models.CharField(blank=True, default='', max_length=200),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='transaction',
            name='transcript',
            field=models.TextField(blank=True, default=''),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='transaction',
            name='type',
            field=models.CharField(blank=True, default='', max_length=200),
            preserve_default=False,
        ),
    ]
