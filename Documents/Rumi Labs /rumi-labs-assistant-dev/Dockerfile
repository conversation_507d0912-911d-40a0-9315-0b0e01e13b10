# Use the official image as a parent image
FROM tiangolo/uvicorn-gunicorn:python3.11 AS builder

# Set the working directory
WORKDIR /app

# Copy the current directory contents into the container at /app
COPY . /app

# Install any needed packages specified in requirements.txt
RUN pip install --no-cache-dir -r requirements.txt

# Expose port 80
EXPOSE 80

# Set environment variable for ALLOWED_HOSTS to allow all hosts
ENV ALLOWED_HOSTS=*

# Run the server using Gunicorn
#CMD ["gunicorn", "-w", "4", "-k", "uvicorn.workers.UvicornWorker", "rumi_labs_assistant.asgi:application", "--bind", "0.0.0.0:80"]
CMD ["python", "manage.py", "runserver", "0.0.0.0:80"]
