from django.http import JsonResponse
from django.conf import settings
from accounts.models import Organization
from accounts.permissions import Is<PERSON><PERSON>r<PERSON><PERSON>Only, <PERSON><PERSON><PERSON><PERSON>Only, <PERSON><PERSON><PERSON><PERSON>Only
from health_care.models import Transaction
from project_helper.aws_helper import AWSHelper
import json
from django.utils.dateparse import parse_datetime
from datetime import datetime, timedelta, time
from rest_framework.views import APIView
from django.utils.timezone import make_aware, get_current_timezone, now as timezone_now
from django.db.models.functions import TruncDate
from django.db.models import Count,Max, Min


CLOUD_WATCH_REGION = settings.AWS_REGION_NAME


aws_helper = AWSHelper(region_name=settings.AWS_REGION_NAME)
class CloudWatchLogListAPIView(APIView):
    permission_classes = [IsSuperUserOnly | IsAdminOnly]
    
    
    def get(self, request, *args, **kwargs):
        user_id = request.GET.get('user_id')
        endpoint = request.GET.get('endpoint')
        status = request.GET.get('status')
        start_time = request.GET.get('start_date')
        end_time = request.GET.get('end_date')
        stream_name = "general"
        client = aws_helper.cloudwatch_client

        # Build the filter pattern
        filter_conditions = []

        if user_id:
            filter_conditions.append(f'"{user_id}"')
        organization_name = self.request.user.organization.name
        filter_conditions.append(f'"{organization_name}"')
        if endpoint:
            filter_conditions.append(f'"{endpoint}"')
        if status:
            filter_conditions.append(f'"{status}"')

        filter_pattern = ' '.join(filter_conditions)
        if start_time:
            start_time_epoch = int(parse_datetime(start_time).timestamp() * 1000) if start_time else None
        else:
            date_time = datetime(2024, 1, 1)
            start_time_epoch = int(date_time.timestamp() * 1000)

        if end_time:
            end_time_dt = parse_datetime(end_time)
            end_of_day = datetime.combine(end_time_dt.date(), datetime.max.time())  # 23:59:59.999999
        else:
            today = datetime.now()
            end_of_day = datetime.combine(today.date(), datetime.max.time())
        end_time_epoch = int(end_of_day.timestamp() * 1000)

        # Fetch logs using FilterLogEvents with the filter pattern and time range
        response = client.filter_log_events(
            logGroupName=organization_name,
            logStreamNames = [stream_name],
            filterPattern=filter_pattern if filter_pattern else None,
            startTime=start_time_epoch,
            endTime=end_time_epoch
        )

        logs = [json.loads(event['message']) for event in response['events']]
        return JsonResponse(logs, safe=False)

class GetCriticalApisLogs(APIView):

    permission_classes = [IsSuperUserOnly]
    def get(self, request, *args, **kwargs):
        start_time = request.GET.get('start_date')
        end_time = request.GET.get('end_date')
        org_id = request.GET.get("organization")
        organization_name = Organization.objects.filter(id=org_id).first().name
        client = aws_helper.cloudwatch_client
        stream_name = "critical_apis"

        if start_time:
            start_time_epoch = int(parse_datetime(start_time).timestamp() * 1000) if start_time else None
        else:
            date_time = datetime(2024, 1, 1)
            start_time_epoch = int(date_time.timestamp() * 1000)

        if end_time:
            end_time_dt = parse_datetime(end_time)
            end_of_day = datetime.combine(end_time_dt.date(), datetime.max.time())  # 23:59:59.999999
        else:
            today = datetime.now()
            end_of_day = datetime.combine(today.date(), datetime.max.time())
        end_time_epoch = int(end_of_day.timestamp() * 1000)

        response = client.filter_log_events(
            logGroupName=organization_name,
            logStreamNames=[stream_name],
            startTime=start_time_epoch,
            endTime=end_time_epoch
        )

        logs = [json.loads(event['message']) for event in response['events']]
        return JsonResponse(logs, safe=False)


## Dashboard ##

class DashboardView(APIView):
    permission_classes = [IsDoctorOnly]

    def get(self, request, *args, **kwargs):
        user = request.user
        tz = get_current_timezone()
        now = timezone_now()
        today = now.date()

        # Base dates
        today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
        yesterday_start = today_start - timedelta(days=1)
        today_end = today_start + timedelta(days=1)

        # Query yesterday and today's transactions
        recent_transactions = self.get_transactions(yesterday_start, today_end, user)

        todays_transactions = recent_transactions.filter(uploaded_at__gte=today_start)
        yesterdays_transactions = recent_transactions.filter(uploaded_at__lt=today_start)

        data = {
            "patients_seen_today": {"count":todays_transactions.count(), "duration": self.get_total_duration(todays_transactions)},
            "patients_seen_yesterday": {"count":yesterdays_transactions.count(), "duration":self.get_total_duration(yesterdays_transactions)}
        }

        # Weekly summary
        this_monday = today - timedelta(days=today.weekday())
        last_monday = this_monday - timedelta(days=7)

        this_week_start = self.aware_datetime(this_monday, time.min, tz)
        this_week_end = self.aware_datetime(today, time.max, tz)
        last_week_start = self.aware_datetime(last_monday, time.min, tz)

        # Add weekly transaction summary grouped by weekday
        data["week_summary"] = self.get_weekly_summary(this_week_start, this_week_end, user)

        # Previous week total transactions
        previous_week_transactions = self.get_transactions(
            last_week_start, this_week_start, user
        )
        data["patients_seen_last_week"] = {"count" : previous_week_transactions.count(), "duration" : self.get_total_duration(previous_week_transactions)}

        return JsonResponse(data)

    def get_transactions(self, start_dt, end_dt, user):
        """Fetch transactions in date range for a specific user."""
        return Transaction.objects.filter(
            uploaded_at__gte=start_dt,
            uploaded_at__lt=end_dt,
            uploaded_by=user
        )

    def get_total_duration(self, transactions):
        """Sum durations from audio_file_meta."""
        return sum(
            int(tx.audio_file_meta.get("duration", 0)) for tx in transactions
        )

    def get_weekly_summary(self, start_dt, end_dt, user):
        """Group transactions by weekday (0=Mon, ..., 6=Sun)."""
        weekly_transactions = Transaction.objects.filter(uploaded_at__range=(start_dt, end_dt), uploaded_by=user)
        grouped = (
            weekly_transactions
            .annotate(day=TruncDate("uploaded_at"))
            .values("day")
            .annotate(total=Count("id"))
        )
        max_api_time = weekly_transactions.aggregate(Max('api_time'))['api_time__max']
        min_api_time = weekly_transactions.aggregate(Min('api_time'))['api_time__min']

        # Get the transactions with those api times
        transaction_with_max_api_time = Transaction.objects.filter(api_time=max_api_time).first()
        transaction_with_min_api_time = Transaction.objects.filter(api_time=min_api_time).first()
        max = [transaction_with_max_api_time.audio_file_meta.get("duration", 0),transaction_with_max_api_time.api_time]
        min = [transaction_with_min_api_time.audio_file_meta.get("duration", 0),transaction_with_min_api_time.api_time]
        duration = self.get_total_duration(weekly_transactions)
        return {"bar_chart":{item["day"].weekday(): item["total"] for item in grouped},"total_duration" : duration,"max_time": max, "min_time":min}

    def aware_datetime(self, date_obj, time_obj, tz):
        """Combine date and time, then make timezone-aware."""
        return make_aware(datetime.combine(date_obj, time_obj), timezone=tz)
