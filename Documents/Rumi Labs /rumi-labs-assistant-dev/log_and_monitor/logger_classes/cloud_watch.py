import datetime
from project_helper.aws_helper import <PERSON><PERSON><PERSON><PERSON><PERSON>
from .base_logger import Logger
from django.conf import settings
import json

AWS_REGION = settings.AWS_REGION_NAME


class CloudWatchLogger(Logger):
    def __init__(self):
        super().__init__()
        aws_helper = AWSHelper(region_name=AWS_REGION)
        self.client = aws_helper.cloudwatch_client  # Replace 'your-region'


    def log(self, user_id: int, organization: str, endpoint: str, date_time: str, status:str,log_group_name):
        if user_id == -1:
            log_group_name = "Anonymous-RumiLabs"
        log_data = {
            'user_id': user_id,
            'organization': organization,
            'endpoint': endpoint,
            'datetime': date_time,
            'status':status
        }
        if endpoint in self.critical_apis:
            self._log(log_data,log_group_name,"critical_apis")

        self._log(log_data, log_group_name, "general")

    def _log(self,log_data,log_group_name,log_stream_name):
        response = self.client.describe_log_streams(logGroupName=log_group_name, logStreamNamePrefix=log_stream_name)
        log_streams = response.get('logStreams', [])
        sequence_token = log_streams[0].get('uploadSequenceToken', None) if log_streams else None

        # Send the log data
        log_event = {
            'logGroupName': log_group_name,
            'logStreamName': log_stream_name,
            'logEvents': [
                {
                    'timestamp': int(datetime.datetime.now().timestamp() * 1000),
                    'message': json.dumps(log_data)
                }
            ]
        }

        if sequence_token:
            log_event['sequenceToken'] = sequence_token

        self.client.put_log_events(**log_event)