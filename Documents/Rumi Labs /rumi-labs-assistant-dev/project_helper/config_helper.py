# In your_app/utils/config_reader.py

import configparser
import os


class Singleton(type):
    _instances = {}

    def __call__(cls, *args, **kwargs):
        if cls not in cls._instances:
            instance = super(Singleton, cls).__call__(*args, **kwargs)
            cls._instances[cls] = instance
            instance._initialized = False  # Custom attribute to track initialization
        else:
            instance = cls._instances[cls]

        # Only call __init__ if this is the first time the instance is being initialized
        if not instance._initialized:
            instance.__init__(*args, **kwargs)
            instance._initialized = True

        return instance


class ConfigHelper(metaclass=Singleton):
    def __init__(self):
        self.config = self._load_config('config.ini')

    def _load_config(self, file_path):
        config = configparser.ConfigParser()
        if not os.path.isfile(file_path):
            raise FileNotFoundError(f"Configuration file not found: {file_path}")
        config.read(file_path)
        config_dict = {section: dict(config.items(section)) for section in config.sections()}

        return config_dict


    def get_critical_apis_config(self):
        return self.config["logs"]['critical_endpoints']
