from drf_yasg.views import get_schema_view
from drf_yasg import openapi
from rest_framework import permissions

# ... existing imports ...

schema_view = get_schema_view(
   openapi.Info(
      title="Accounts API",
      default_version='v1',
      description="API documentation for Accounts",
      terms_of_service="https://www.google.com/policies/terms/",
      contact=openapi.Contact(email="<EMAIL>"),
      license=openapi.License(name="BSD License"),
   ),
   public=True,
   permission_classes=(permissions.AllowAny,),
)