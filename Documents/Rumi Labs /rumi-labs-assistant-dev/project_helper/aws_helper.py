from boto3 import Session
from botocore.exceptions import ClientError, NoCredentialsError
from dotenv import load_dotenv
import os

load_dotenv()

AWS_SERVER_PUBLIC_KEY = os.getenv("AWS_SERVER_PUBLIC_KEY")
AWS_SERVER_SECRET_KEY = os.getenv("AWS_SERVER_SECRET_KEY")
from anthropic import AnthropicBedrock

region_name = os.getenv("AWS_REGION_NAME")


class SingletonPerRegion(type):
    _instances = {}

    def __call__(cls, *args, **kwargs):
        # Assume the region is passed as a keyword argument
        region = kwargs.get('region_name')
        if not region:
            raise ValueError("A 'region_name' keyword argument is required.")

        # Use (cls, region) as the key to store instances
        if (cls, region) not in cls._instances:
            instance = super(SingletonPerRegion, cls).__call__(*args, **kwargs)
            cls._instances[(cls, region)] = instance
            instance._initialized = False  # Track initialization per region
        else:
            instance = cls._instances[(cls, region)]

        # Only call __init__ if this is the first time the instance is being initialized
        if not instance._initialized:
            instance.__init__(*args, **kwargs)
            instance._initialized = True

        return instance


class AWSHelper(metaclass=SingletonPerRegion):
    def __init__(self, region_name):
        # Initialize the AWS session with the given region
        self.session = Session(
            aws_access_key_id=AWS_SERVER_PUBLIC_KEY,
            aws_secret_access_key=AWS_SERVER_SECRET_KEY,
            region_name=region_name
        )
        self.cognito_client = self.session.client("cognito-idp")
        self.s3_client = self.session.client('s3')
        self.sqs_client = self.session.client('sqs')
        self.cloudtrail_client = self.session.client('cloudtrail')
        self.lambda_client = self.session.client('lambda')
        self.bedrock_client = self.session.client('bedrock-runtime')
        self.cloudwatch_client = self.session.client("logs")
        self.secret_manager_client = self.session.client(service_name="secretsmanager")
        self.anthropic_bedrock_client = AnthropicBedrock(aws_access_key=AWS_SERVER_PUBLIC_KEY,
                                        aws_secret_key=AWS_SERVER_SECRET_KEY)

class S3Helper:
    def __init__(self):
        aws_helper = AWSHelper(region_name=region_name)
        self.s3_client = aws_helper.s3_client

    def create_bucket(self, bucket_name):
        try:
            self.s3_client.create_bucket(
                Bucket=bucket_name)
            print(f"Bucket {bucket_name} created successfully.")
        except Exception as e:
            print(f"Error creating bucket: {e}")
            raise e

    def delete_s3_bucket(self, bucket_name):
        try:
            self.delete_all_objects_from_bucket(bucket_name)
            self.s3_client.delete_bucket(Bucket=bucket_name)
            print(f"Bucket {bucket_name} deleted successfully.")
        except Exception as e:
            print(f"Error deleting bucket: {e}")
            raise e

    def upload_file_to_bucket(self, file_name, bucket_name, object_name=None):
        try:
            if object_name is None:
                object_name = file_name
            self.s3_client.upload_file(file_name, bucket_name, object_name)
            print(f"File {file_name} uploaded to {bucket_name}/{object_name} successfully.")
            return True
        except Exception as e:
            print(f"Error uploading file: {e}")
            return False

    def read_file_from_bucket(self, bucket_name, file_path):
        try:
            response = self.s3_client.get_object(Bucket=bucket_name, Key=file_path)
            content = response['Body'].read().decode('utf-8')
            print(f"File content from {bucket_name}/{file_path}:\n{content}")
            return content
        except Exception as e:
            print(f"Error reading file: {e}")
            raise e

    def download_file_from_bucket(self, bucket_name, file_path, destination_path):
        try:
            self.s3_client.download_file(bucket_name, file_path, destination_path)
            print(f"File {file_path} downloaded from {bucket_name} to {destination_path} successfully.")
        except Exception as e:
            print(f"Error downloading file: {e}")
            raise e

    def delete_file_from_bucket(self, bucket_name, file_path):
        try:
            self.s3_client.delete_object(Bucket=bucket_name, Key=file_path)
            print(f"File {file_path} deleted from {bucket_name} successfully.")
        except Exception as e:
            print(f"Error deleting file: {e}")
            raise e

    def delete_all_objects_from_bucket(self, bucket_name):
        try:
            # List all objects in the bucket
            response = self.s3_client.list_objects_v2(Bucket=bucket_name)
            if 'Contents' in response:
                for obj in response['Contents']:
                    print(f"Deleting {obj['Key']}...")
                    self.s3_client.delete_object(Bucket=bucket_name, Key=obj['Key'])
                print(f"All objects in bucket {bucket_name} deleted successfully.")
            else:
                print(f"No objects found in bucket {bucket_name}.")
        except Exception as e:
            print(f"Error deleting objects: {e}")
            raise e

    def bucket_exists(self, bucket_name):
        try:
            response = self.s3_client.list_buckets()
            bucket_names = [bucket['Name'] for bucket in response.get('Buckets', [])]
            return bucket_name in bucket_names
        except Exception as e:
            print(f"Error checking if bucket exists: {e}")
            return False

    def file_exists_on_bucket(self, bucket_name, file_path):
        try:
            self.s3_client.head_object(Bucket=bucket_name, Key=file_path)
            return True
        except self.s3_client.exceptions.ClientError as e:
            # If the error is 404, it means the file does not exist
            if e.response['Error']['Code'] == '404':
                return False
            else:
                print(f"Error checking if file exists: {e}")
                return False

    def upload_file_obj_to_bucket(self, file_obj, bucket_name, object_name):
        try:
            self.s3_client.upload_fileobj(file_obj, bucket_name, object_name)
            return True
        except Exception as e:
            print(f"Error uploading file to S3: {e}")
            return False

    def create_presigned_url(self,bucket_name, object_name, expiration=3600):
        try:
            response = self.s3_client.generate_presigned_url('get_object',
                                                        Params={'Bucket': bucket_name, 'Key': object_name},
                                                        ExpiresIn=expiration)
        except NoCredentialsError:
            print("AWS credentials not found.")
            return None
        except Exception as e:
            print(f"Error generating pre-signed URL: {e}")
            return None

        return response


if __name__ == "__main__":
    s3_helper1 = AWSHelper(region_name="us-east-1")
    s3_helper2 = AWSHelper(region_name="us-east-1")
    s3_helper3 = AWSHelper(region_name="us-west-2")
    s3_helper4 = AWSHelper(region_name="us-west-2")

    print(s3_helper1 is s3_helper2)
    print(s3_helper1 is s3_helper3)
    print(s3_helper3 is s3_helper4)

