from rest_framework.response import Response
from typing import Any
from rest_framework import status


class ResponseMixin:
    """Mixin to standardize API responses"""
    
    def success_response(self, data: Any = None, message: str = None, status_code: int = status.HTTP_200_OK) -> Response:
        response_data = {
            "success": True,
            "message": message,
            "data": data
        }
        return Response(response_data, status=status_code)

    def bad_request_response(self, message: str, status_code: int = status.HTTP_400_BAD_REQUEST) -> Response:
        return Response({
            "success": False,
            "error": message
        }, status=status_code)
    
    def success_created_response(self, data: Any = None, message: str = None, status_code: int = status.HTTP_201_CREATED) -> Response:
        return Response({
            "success": True,
            "message": message,
            "data": data
        }, status=status_code)
    

    def internal_server_error_response(self, message: str = None, status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR) -> Response:
        return Response({
            "success": False,
            "error": message
        }, status=status_code)
    
    def forbidden_response(self, message: str = None, status_code: int = status.HTTP_403_FORBIDDEN) -> Response:
        return Response({
            "success": False,
            "error": message
        }, status=status_code)
    
    