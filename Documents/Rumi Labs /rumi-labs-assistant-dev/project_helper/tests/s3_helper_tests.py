import os
import time
import unittest
from ..aws_helper import S3Helper


class TestS3Helper(unittest.TestCase):
    expected_data1 = "This is a test file."
    @classmethod
    def setUpClass(cls):
        cls.s3_helper = S3Helper()
        cls.bucket_name = "test-bucket-33443311"
        cls.file1_name = "file1.txt"
        cls.file2_name = "file2.txt"
        cls.file3_name = "file3.txt"
        cls.file_data1 = "This is a test file."

        with open(cls.file1_name,"w") as f:
            f.write(cls.file_data1)
        with open(cls.file2_name,"w") as f:
            f.write(cls.file_data1)
        with open(cls.file3_name,"w") as f:
            f.write(cls.file_data1)

    def setUp(self):
        time.sleep(1)

    def test_a_create_bucket(self):
        bucket_name = self.bucket_name
        self.s3_helper.create_bucket(bucket_name)
        assert self.s3_helper.bucket_exists(bucket_name)

    def test_b_upload_file(self):
        bucket_name = self.bucket_name

        self.s3_helper.upload_file_to_bucket(self.file1_name, bucket_name, self.file1_name)
        assert  self.s3_helper.file_exists_on_bucket(bucket_name,self.file1_name)
        self.s3_helper.upload_file_to_bucket(self.file2_name, bucket_name, self.file2_name)
        assert self.s3_helper.file_exists_on_bucket(bucket_name, self.file2_name)
        self.s3_helper.upload_file_to_bucket(self.file3_name, bucket_name, self.file3_name)
        assert self.s3_helper.file_exists_on_bucket(bucket_name, self.file3_name)

    def test_c_read_file_from_bucket(self):
        bucket_name = self.bucket_name
        expected_content = self.expected_data1

        content = self.s3_helper.read_file_from_bucket(bucket_name, self.file1_name)
        self.assertEqual(content, expected_content)
    def test_d_download_file(self):
        bucket_name = self.bucket_name
        dest_file_name = "downloaded_file.txt"
        expected_content = self.expected_data1

        self.s3_helper.download_file_from_bucket(bucket_name, self.file1_name, dest_file_name)
        with open(dest_file_name,"r") as f:
            content = f.read()
            self.assertEqual(content, expected_content)
        os.remove(dest_file_name)
    def test_e_delete_file_from_bucket(self):
        bucket_name = self.bucket_name

        self.s3_helper.delete_file_from_bucket(bucket_name, self.file1_name)
        assert not self.s3_helper.file_exists_on_bucket(bucket_name,self.file1_name)
    def test_f_delete_all_objects_from_bucket(self):
        bucket_name = self.bucket_name

        self.s3_helper.delete_all_objects_from_bucket(bucket_name)
        assert not self.s3_helper.file_exists_on_bucket(bucket_name,self.file2_name)
        assert not self.s3_helper.file_exists_on_bucket(bucket_name,self.file3_name)


    def test_g_delete_s3_bucket(self):
        bucket_name = self.bucket_name
        self.s3_helper.delete_s3_bucket(bucket_name)
        assert not self.s3_helper.bucket_exists(bucket_name)

    @classmethod
    def tearDownClass(cls):
        os.remove(cls.file1_name)
        os.remove(cls.file2_name)
        os.remove(cls.file3_name)


if __name__ == '__main__':
    unittest.main()
