#!/usr/bin/env python3
"""
Enhanced HIPAA Compliance Scanner

This script analyzes a codebase for potential HIPAA compliance issues by scanning
for patterns that might indicate security vulnerabilities or non-compliant practices.
Enhanced with additional checks for modern healthcare applications.
"""

import os
import re
import json
import hashlib
import logging
from datetime import datetime
import argparse
import zipfile
from collections import defaultdict, Counter
from pathlib import Path
import yaml
import configparser

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

# Enhanced patterns for HIPAA compliance issues
PATTERNS = {
    # Authentication & Authorization (Enhanced)
    "weak_auth": [
        r"admin['\"]\s*[:=]\s*['\"]admin['\"]",
        r"password['\"]\s*[:=]\s*['\"][^'\"]{1,8}['\"]",
        r"hardcoded[_\s]*password",
        r"default[_\s]*password",
        r"\bauth\s*=\s*None\b",
        r"\bauth_required\s*=\s*False\b",
        r"authenticate\s*=\s*False",
        r"bypass[_\s]*auth",
        r"skip[_\s]*auth",
        r"no[_\s]*auth",
        r"DISABLE[_\s]*AUTH",
        r"allow[_\s]*anonymous",
        r"guest[_\s]*access\s*=\s*True",
        r"JWT_SECRET\s*=\s*['\"][^'\"]{1,16}['\"]",  # Weak JWT secrets
        r"SECRET[_\s]*KEY\s*=\s*['\"][^'\"]{1,16}['\"]",  # Short secret keys
    ],
    
    # Encryption (Enhanced)
    "encryption_issues": [
        r"http://",  # Non-encrypted connections
        r"\.send\(.*password",  # Sending passwords without explicit encryption
        r"encryption\s*=\s*False",
        r"encrypt\s*=\s*False",
        r"ssl_verify\s*=\s*False",
        r"verify_ssl\s*=\s*False",
        r"checkServerIdentity\s*=\s*False",
        r"TLS_VERIFY\s*=\s*False",
        r"CERT_NONE",  # SSL context with no certificate verification
        r"MD5|SHA1",  # Weak hashing algorithms
        r"DES|3DES",  # Weak encryption algorithms
        r"RC4",  # Insecure cipher
        r"ssl_context\s*=\s*None",
        r"verify_mode\s*=\s*ssl\.CERT_NONE",
        r"PROTOCOL_TLS\s*=\s*False",
    ],
    
    # Data Exposure (Enhanced)
    "data_exposure": [
        r"\.debug\(",
        r"print\([^)]*(?:patient|medical|health|ssn|social|dob|diagnosis)",
        r"console\.log\([^)]*(?:patient|medical|health|ssn|social|dob|diagnosis)",
        r"\.log\([^)]*(?:patient|medical|health|ssn|social|dob|diagnosis)",
        r"logger\.(?:info|debug|warn)\([^)]*(?:patient|medical|health|ssn|social|dob|diagnosis)",
        r"traceback\.print_exc\(\)",  # May expose sensitive data in stack traces
        r"pdb\.set_trace\(\)",  # Debug breakpoints in production
        r"debugger;",  # JavaScript debugger statements
        r"var_dump\(",  # PHP debug output
        r"dump\([^)]*(?:patient|medical|health)",
    ],
    
    # PHI (Protected Health Information) handling (Enhanced)
    "phi_handling": [
        r"\.csv",  # Potential structured data files
        r"\.xlsx?",  # Excel files
        r"\.json",  # JSON files that might contain PHI
        r"\.xml",   # XML files
        r"database\.connect",
        r"mongo\.connect",
        r"mongoose\.connect",
        r"new\s+SQL",
        r"sqlite",
        r"redis\.createClient",
        r"psycopg2\.connect",
        r"pymongo\.MongoClient",
        r"mysql\.connector",
        r"CREATE\s+TABLE",
        r"INSERT\s+INTO",
        r"SELECT\s+\*\s+FROM",
        r"UPDATE\s+.*\s+SET",
        r"DELETE\s+FROM",
    ],
    
    # Audit logging (Enhanced)
    "audit_concerns": [
        r"audit\s*=\s*False",
        r"logging\s*=\s*False",
        r"log\s*=\s*False",
        r"disable[_\s]*logging",
        r"disable[_\s]*audit",
        r"NO_AUDIT",
        r"AUDIT_DISABLED",
        r"log[_\s]*level\s*=\s*(?:CRITICAL|ERROR)",  # Too restrictive logging
        r"silence[_\s]*logs",
        r"quiet[_\s]*mode\s*=\s*True",
    ],
    
    # Session handling (Enhanced)
    "session_issues": [
        r"session\.timeout\s*=\s*([0-9]+)",  # Check for timeout values
        r"cookie[_\s]*secure\s*=\s*False",
        r"httpOnly\s*=\s*False",
        r"sameSite\s*=\s*['\"]none['\"]",
        r"SESSION_TIMEOUT\s*=\s*[0-9]+",
        r"session[_\s]*lifetime\s*=\s*[0-9]+",
        r"remember[_\s]*me\s*=\s*True",
        r"permanent[_\s]*session\s*=\s*True",
        r"session\.permanent\s*=\s*True",
    ],
    
    # Third-party integration (Enhanced)
    "third_party": [
        r"api[_\s]*key\s*=\s*['\"][^'\"]+['\"]",
        r"apikey\s*=\s*['\"][^'\"]+['\"]",
        r"api[_\s]*secret\s*=\s*['\"][^'\"]+['\"]",
        r"app[_\s]*id\s*=\s*['\"][^'\"]+['\"]",
        r"app[_\s]*secret\s*=\s*['\"][^'\"]+['\"]",
        r"aws[_\s]*(?:access_key|secret)",
        r"github[_\s]*token",
        r"stripe[_\s]*(?:key|secret)",
        r"twilio[_\s]*(?:sid|token)",
        r"sendgrid[_\s]*api[_\s]*key",
        r"deepgram[_\s]*(?:key|token|api)",
        r"openai[_\s]*api[_\s]*key",
        r"google[_\s]*api[_\s]*key",
        r"azure[_\s]*(?:key|secret)",
    ],

    # Data retention (Enhanced)
    "data_retention": [
        r"backup[_\s]*enabled\s*=\s*False",
        r"retention[_\s]*period\s*=\s*[0-9]+",
        r"delete[_\s]*after\s*=\s*[0-9]+",
        r"expire[_\s]*after",
        r"TTL\s*=\s*[0-9]+",
        r"time[_\s]*to[_\s]*live",
        r"auto[_\s]*delete\s*=\s*False",
        r"purge[_\s]*old[_\s]*data\s*=\s*False",
    ],
    
    # PHI patterns (Enhanced with more specific patterns)
    "phi_patterns": [
        r"\b\d{3}-\d{2}-\d{4}\b",  # SSN pattern
        r"\b\d{9}\b",  # Potential SSN without dashes
        r"\bpatient[_\s]*(?:id|number|identifier)\b",
        r"\bmedical[_\s]*record[_\s]*(?:number|id)\b",
        r"\bhealth[_\s]*record\b",
        r"\bdiagnosis[_\s]*code\b",
        r"\bicd[_\s]*(?:9|10)[_\s]*code\b",
        r"\bcpt[_\s]*code\b",
        r"\btreatment[_\s]*plan\b",
        r"\bprocedure[_\s]*code\b",
        r"\bmedication[_\s]*(?:name|code)\b",
        r"\bprescription[_\s]*(?:id|number)\b",
        r"\bdob\b",  # Date of birth
        r"\bdate[_\s]*of[_\s]*birth\b",
        r"\bbirth[_\s]*date\b",
        r"\baddress\b",
        r"\bemail[_\s]*address\b",
        r"\bphone[_\s]*number\b",
        r"\binsurance[_\s]*(?:id|number|carrier)\b",
        r"\bclaim[_\s]*(?:id|number)\b",
        r"\bmrn\b",  # Medical Record Number
        r"\bnpi\b",  # National Provider Identifier
    ],

    # AWS specific security issues
    "aws_security": [
        r"PublicRead|PublicReadWrite",  # S3 bucket permissions
        r"s3[_\s]*bucket[_\s]*public",
        r"IAM[_\s]*policy.*\*",  # Overly permissive IAM policies
        r"Principal.*\*",  # Wildcard principals
        r"Action.*\*",  # Wildcard actions
        r"Resource.*\*",  # Wildcard resources
        r"aws[_\s]*access[_\s]*key[_\s]*id\s*=",
        r"aws[_\s]*secret[_\s]*access[_\s]*key\s*=",
        r"VPC[_\s]*security[_\s]*group.*0\.0\.0\.0/0",  # Open security groups
        r"ingress.*0\.0\.0\.0/0",
        r"egress.*0\.0\.0\.0/0",
    ],

    # API security issues
    "api_security": [
        r"CORS.*origin.*\*",  # Permissive CORS
        r"Access-Control-Allow-Origin.*\*",
        r"rate[_\s]*limit\s*=\s*(?:None|False|0)",
        r"throttle\s*=\s*(?:None|False)",
        r"api[_\s]*version\s*=\s*['\"]v?1['\"]",  # Old API versions
        r"@app\.route\([^)]*methods\s*=\s*\[[^]]*['\"]GET['\"][^]]*['\"]POST['\"]",  # Mixed methods
        r"request\.args\.get\([^)]*\)",  # Unsanitized input
        r"request\.form\.get\([^)]*\)",
        r"json\.loads\(request\.data\)",  # Direct JSON parsing
    ],

    # Input validation issues
    "input_validation": [
        r"eval\(",  # Code injection risks
        r"exec\(",
        r"subprocess\.(?:call|run|Popen).*shell\s*=\s*True",
        r"os\.system\(",
        r"sql.*%.*%",  # String formatting in SQL
        r"query.*format\(",  # String formatting in queries
        r"f['\"].*\{.*\}.*['\"].*(?:SELECT|INSERT|UPDATE|DELETE)",  # F-strings in SQL
        r"\.format\(.*\).*(?:SELECT|INSERT|UPDATE|DELETE)",
        r"innerHTML\s*=",  # XSS risks
        r"document\.write\(",
    ],

    # Configuration security
    "config_security": [
        r"DEBUG\s*=\s*True",
        r"TESTING\s*=\s*True",
        r"DEVELOPMENT\s*=\s*True",
        r"ENV\s*=\s*['\"]dev",
        r"ENVIRONMENT\s*=\s*['\"]development",
        r"FLASK_ENV\s*=\s*development",
        r"NODE_ENV\s*=\s*development",
        r"DJANGO_DEBUG\s*=\s*True",
    ],
}

# Enhanced dependency files
DEPENDENCY_FILES = [
    "requirements.txt",
    "requirements-dev.txt", 
    "requirements-prod.txt",
    "package.json",
    "package-lock.json",
    "yarn.lock",
    "Pipfile",
    "Pipfile.lock",
    "poetry.lock",
    "pyproject.toml",
    "Cargo.toml",
    "Cargo.lock",
    "go.mod",
    "go.sum",
    "composer.json",
    "composer.lock",
]

# Enhanced security packages
SECURITY_PACKAGES = [
    # Python Security
    "cryptography", "pyopenssl", "bcrypt", "passlib", "argon2-cffi",
    "django-oauth-toolkit", "python-jose", "pyjwt", "oauthlib", "authlib",
    "flask-security", "flask-talisman", "django-allauth", "bandit",
    "safety", "pip-audit", "semgrep", "django-security", "flask-seasurf",
    "django-csp", "secure", "python-decouple", "environs",
    
    # JavaScript/Node.js Security
    "helmet", "passport", "jsonwebtoken", "crypto-js", "bcrypt", "bcryptjs",
    "express-session", "csrf", "csurf", "snyk", "auth0", "node-forge",
    "express-rate-limit", "cors", "hpp", "express-validator", "joi",
    "audit", "npm-audit", "retire", "nsp", "eslint-plugin-security",
    
    # Cloud Security
    "boto3-stubs", "aws-cdk", "localstack", "moto",
    
    # Monitoring & Logging
    "sentry-sdk", "datadog", "newrelic", "loguru", "structlog",
]

# File extensions to analyze
SECURITY_RELEVANT_EXTENSIONS = {
    '.py', '.js', '.ts', '.jsx', '.tsx', '.java', '.cs', '.php', '.rb',
    '.go', '.rs', '.cpp', '.c', '.h', '.hpp', '.sql', '.yml', '.yaml',
    '.json', '.xml', '.ini', '.cfg', '.conf', '.env', '.properties',
    '.sh', '.bash', '.ps1', '.bat', '.dockerfile', '.tf', '.hcl'
}

def scan_file(filepath, patterns):
    """Enhanced file scanning with better pattern matching."""
    try:
        with open(filepath, 'r', encoding='utf-8', errors='ignore') as file:
            content = file.read()
            
            results = defaultdict(list)
            lines = content.split('\n')
            
            for line_number, line in enumerate(lines, 1):
                for category, regex_list in patterns.items():
                    for regex in regex_list:
                        try:
                            matches = re.finditer(regex, line, re.IGNORECASE)
                            for match in matches:
                                # Additional context for better analysis
                                context_start = max(0, line_number - 2)
                                context_end = min(len(lines), line_number + 1)
                                context_lines = lines[context_start:context_end]
                                
                                results[category].append({
                                    'line': line_number,
                                    'text': line.strip(),
                                    'match': match.group(0),
                                    'filepath': filepath,
                                    'context': context_lines,
                                    'severity': classify_severity(category, match.group(0))
                                })
                        except re.error as e:
                            logger.warning(f"Invalid regex pattern '{regex}': {e}")
                            
            return results
    except Exception as e:
        logger.error(f"Error scanning file {filepath}: {e}")
        return {"error": [{"filepath": filepath, "message": str(e)}]}

def classify_severity(category, match_text):
    """Classify the severity of findings."""
    high_severity_patterns = [
        'hardcoded', 'admin', 'password', 'secret', 'eval', 'exec', 
        'shell=True', 'DEBUG=True', 'ssl_verify=False'
    ]
    
    medium_severity_patterns = [
        'http://', 'print(', 'console.log', 'timeout', 'CORS'
    ]
    
    match_lower = match_text.lower()
    
    if any(pattern.lower() in match_lower for pattern in high_severity_patterns):
        return 'HIGH'
    elif any(pattern.lower() in match_lower for pattern in medium_severity_patterns):
        return 'MEDIUM'
    else:
        return 'LOW'

def check_config_files(root_dir):
    """Check configuration files for security issues."""
    config_findings = {
        "env_files": [],
        "config_files": [],
        "sensitive_configs": []
    }
    
    config_patterns = [
        '.env', '.env.local', '.env.production', '.env.development',
        'config.ini', 'config.cfg', 'settings.py', 'config.py',
        'application.properties', 'application.yml', 'application.yaml'
    ]
    
    for root, _, files in os.walk(root_dir):
        for filename in files:
            if any(pattern in filename.lower() for pattern in config_patterns):
                filepath = os.path.join(root, filename)
                config_findings["config_files"].append(filepath)
                
                if '.env' in filename:
                    config_findings["env_files"].append(filepath)
                
                # Check for sensitive information in config files
                try:
                    with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                        sensitive_patterns = [
                            r'password\s*=', r'secret\s*=', r'key\s*=',
                            r'token\s*=', r'api_key\s*=', r'database_url\s*='
                        ]
                        
                        for pattern in sensitive_patterns:
                            if re.search(pattern, content, re.IGNORECASE):
                                config_findings["sensitive_configs"].append({
                                    "file": filepath,
                                    "pattern": pattern
                                })
                except Exception as e:
                    logger.error(f"Error reading config file {filepath}: {e}")
    
    return config_findings

def check_aws_infrastructure(root_dir):
    """Check for AWS infrastructure as code files."""
    aws_findings = {
        "terraform_files": [],
        "cloudformation_files": [],
        "cdk_files": [],
        "serverless_files": []
    }
    
    for root, _, files in os.walk(root_dir):
        for filename in files:
            filepath = os.path.join(root, filename)
            
            if filename.endswith(('.tf', '.hcl')):
                aws_findings["terraform_files"].append(filepath)
            elif filename.endswith(('.yaml', '.yml', '.json')) and 'cloudformation' in filename.lower():
                aws_findings["cloudformation_files"].append(filepath)
            elif 'cdk' in filename.lower() and filename.endswith(('.py', '.js', '.ts')):
                aws_findings["cdk_files"].append(filepath)
            elif filename in ['serverless.yml', 'serverless.yaml']:
                aws_findings["serverless_files"].append(filepath)
    
    return aws_findings

def analyze_deepgram_integration(root_dir):
    """Analyze Deepgram API integration for HIPAA compliance."""
    deepgram_findings = {
        "integration_files": [],
        "potential_issues": []
    }
    
    deepgram_patterns = [
        r'deepgram', r'@deepgram', r'deepgram\.com',
        r'dg_.*key', r'deepgram.*api.*key'
    ]
    
    for root, _, files in os.walk(root_dir):
        for filename in files:
            if filename.endswith(tuple(SECURITY_RELEVANT_EXTENSIONS)):
                filepath = os.path.join(root, filename)
                
                try:
                    with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                        
                        for pattern in deepgram_patterns:
                            if re.search(pattern, content, re.IGNORECASE):
                                deepgram_findings["integration_files"].append(filepath)
                                break
                        
                        # Check for potential HIPAA issues with Deepgram
                        hipaa_concerns = [
                            r'tier.*standard',  # Should use enhanced tier for HIPAA
                            r'redact.*false',   # Should enable redaction
                            r'diarize.*false',  # Consider diarization for patient privacy
                            r'model.*general',  # Should use medical model if available
                        ]
                        
                        for concern in hipaa_concerns:
                            matches = re.finditer(concern, content, re.IGNORECASE)
                            for match in matches:
                                deepgram_findings["potential_issues"].append({
                                    "file": filepath,
                                    "issue": match.group(0),
                                    "recommendation": get_deepgram_recommendation(match.group(0))
                                })
                                
                except Exception as e:
                    logger.error(f"Error analyzing Deepgram integration in {filepath}: {e}")
    
    return deepgram_findings

def get_deepgram_recommendation(issue):
    """Get specific recommendations for Deepgram HIPAA compliance."""
    recommendations = {
        'tier': 'Use Deepgram Enhanced tier for HIPAA-compliant transcription',
        'redact': 'Enable PII redaction to automatically remove sensitive information',
        'diarize': 'Consider enabling diarization to separate speakers for patient privacy',
        'model': 'Use medical-specific models when available for better accuracy'
    }
    
    for key, rec in recommendations.items():
        if key in issue.lower():
            return rec
    
    return 'Review Deepgram configuration for HIPAA compliance'

def scan_directory(directory, excludes=None):
    """Enhanced directory scanning with comprehensive checks."""
    if excludes is None:
        excludes = ['.git', 'node_modules', 'venv', '.venv', '__pycache__', 
                   '.pytest_cache', 'dist', 'build', '.next', 'coverage']
    
    exclude_dirs = set([os.path.join(directory, d) for d in excludes])
    
    all_results = {
        "scan_info": {
            "timestamp": datetime.now().isoformat(),
            "directory": os.path.abspath(directory),
            "scanner_version": "2.0.0",
        },
        "findings": defaultdict(list),
        "stats": {
            "files_scanned": 0,
            "issues_found": 0,
            "categories": {},
            "severity_counts": {"HIGH": 0, "MEDIUM": 0, "LOW": 0}
        },
        "file_extensions": {},
        "security_dependencies": {},
        "ssl_certificates": [],
        "containerization": [],
        "config_analysis": {},
        "aws_infrastructure": {},
        "deepgram_analysis": {},
        "recommendations": []
    }
    
    logger.info(f"Starting enhanced HIPAA compliance scan of directory: {directory}")
    
    # Enhanced analysis components
    all_results["file_extensions"] = analyze_file_extensions(directory)
    all_results["security_dependencies"] = check_dependencies(directory)
    all_results["ssl_certificates"] = check_for_ssl_certificates(directory)
    all_results["containerization"] = check_for_docker_k8s(directory)
    all_results["config_analysis"] = check_config_files(directory)
    all_results["aws_infrastructure"] = check_aws_infrastructure(directory)
    all_results["deepgram_analysis"] = analyze_deepgram_integration(directory)
    
    # Scan files
    for root, dirs, files in os.walk(directory):
        dirs[:] = [d for d in dirs if os.path.join(root, d) not in exclude_dirs]
        
        for filename in files:
            filepath = os.path.join(root, filename)
            _, ext = os.path.splitext(filename)
            
            # Skip binary files and irrelevant extensions
            if ext.lower() not in SECURITY_RELEVANT_EXTENSIONS:
                continue
                
            if is_binary_file(filepath):
                continue
                
            try:
                results = scan_file(filepath, PATTERNS)
                
                for category, findings in results.items():
                    if findings:
                        all_results["findings"][category].extend(findings)
                        all_results["stats"]["issues_found"] += len(findings)
                        
                        if category not in all_results["stats"]["categories"]:
                            all_results["stats"]["categories"][category] = 0
                        all_results["stats"]["categories"][category] += len(findings)
                        
                        # Count severity levels
                        for finding in findings:
                            severity = finding.get('severity', 'LOW')
                            all_results["stats"]["severity_counts"][severity] += 1
                
                all_results["stats"]["files_scanned"] += 1
                
            except Exception as e:
                logger.error(f"Error scanning {filepath}: {e}")
    
    # Generate recommendations
    all_results["recommendations"] = generate_recommendations(all_results)
    
    logger.info(f"Enhanced scan complete. Scanned {all_results['stats']['files_scanned']} files, "
               f"found {all_results['stats']['issues_found']} potential issues.")
    
    return all_results

def is_binary_file(filepath):
    """Check if a file is binary."""
    try:
        with open(filepath, 'rb') as f:
            chunk = f.read(1024)
            return b'\0' in chunk
    except:
        return True

def generate_recommendations(scan_results):
    """Generate specific HIPAA compliance recommendations based on findings."""
    recommendations = []
    
    findings = scan_results["findings"]
    
    # Critical security recommendations
    if findings.get("weak_auth"):
        recommendations.append({
            "category": "Authentication",
            "priority": "CRITICAL",
            "recommendation": "Implement strong authentication with MFA, remove hardcoded credentials",
            "details": "Found weak authentication patterns that could compromise PHI access"
        })
    
    if findings.get("encryption_issues"):
        recommendations.append({
            "category": "Encryption",
            "priority": "CRITICAL", 
            "recommendation": "Enable TLS/SSL for all data transmission, use strong encryption at rest",
            "details": "Encryption issues detected that could expose PHI in transit or at rest"
        })
    
    if findings.get("aws_security"):
        recommendations.append({
            "category": "Cloud Security",
            "priority": "HIGH",
            "recommendation": "Review AWS IAM policies, S3 bucket permissions, and VPC security groups",
            "details": "AWS security misconfigurations detected"
        })
    
    # Deepgram specific recommendations
    if scan_results["deepgram_analysis"]["integration_files"]:
        recommendations.append({
            "category": "Third-party Integration",
            "priority": "HIGH",
            "recommendation": "Ensure Deepgram BAA is signed and Enhanced tier is used for HIPAA compliance",
            "details": "Deepgram integration detected - verify HIPAA compliance measures"
        })
    
    # Configuration recommendations
    if scan_results["config_analysis"]["sensitive_configs"]:
        recommendations.append({
            "category": "Configuration Security",
            "priority": "HIGH",
            "recommendation": "Move sensitive configuration to secure environment variables or secret management",
            "details": "Sensitive information found in configuration files"
        })
    
    # Audit logging recommendations
    if findings.get("audit_concerns"):
        recommendations.append({
            "category": "Audit Controls",
            "priority": "HIGH",
            "recommendation": "Implement comprehensive audit logging for all PHI access and modifications",
            "details": "Insufficient audit logging detected"
        })
    
    return recommendations

# Include all other functions from original code with minor enhancements
def check_dependencies(root_dir):
    """Enhanced dependency checking."""
    findings = {
        "security_packages_found": [],
        "dependency_files_found": [],
        "vulnerable_packages": []
    }
    
    for root, _, files in os.walk(root_dir):
        for filename in files:
            if filename in DEPENDENCY_FILES:
                filepath = os.path.join(root, filename)
                findings["dependency_files_found"].append(filepath)
                
                try:
                    with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read().lower()
                        for package in SECURITY_PACKAGES:
                            if package.lower() in content:
                                findings["security_packages_found"].append({
                                    "file": filepath,
                                    "package": package
                                })
                        
                        # Check for potentially vulnerable packages
                        vulnerable_patterns = [
                            r'django==1\.[0-9]',  # Old Django versions
                            r'flask==0\.[0-9]',   # Old Flask versions
                            r'requests==2\.[0-9]\.[0-9]',  # Old requests versions
                        ]
                        
                        for pattern in vulnerable_patterns:
                            if re.search(pattern, content):
                                findings["vulnerable_packages"].append({
                                    "file": filepath,
                                    "pattern": pattern
                                })
                                
                except Exception as e:
                    logger.error(f"Error reading {filepath}: {e}")
    
    return findings

def check_for_ssl_certificates(root_dir):
    """Enhanced SSL certificate checking."""
    ssl_files = []
    ssl_extensions = ['.pem', '.crt', '.key', '.p12', '.pfx', '.cer', '.der', '.csr']
    
    for root, _, files in os.walk(root_dir):
        for filename in files:
            if any(filename.lower().endswith(ext) for ext in ssl_extensions):
                filepath = os.path.join(root, filename)
                ssl_files.append({
                    "path": filepath,
                    "type": Path(filename).suffix.lower(),
                    "size": os.path.getsize(filepath) if os.path.exists(filepath) else 0
                })
    
    return ssl_files

def analyze_file_extensions(root_dir):
    """Enhanced file extension analysis."""
    extensions = Counter()
    
    for root, _, files in os.walk(root_dir):
        for filename in files:
            _, ext = os.path.splitext(filename)
            if ext:
                extensions[ext.lower()] += 1
    
    return extensions

def check_for_docker_k8s(root_dir):
    """Enhanced containerization file checking."""
    container_files = []
    patterns = {
        'docker': ['Dockerfile', 'docker-compose.yml', 'docker-compose.yaml', '.dockerignore'],
        'kubernetes': ['*.yaml', '*.yml'],
        'helm': ['Chart.yaml', 'values.yaml'],
        'terraform': ['*.tf', '*.hcl']
    }
    
    for root, dirs, files in os.walk(root_dir):
        for filename in files:
            filepath = os.path.join(root, filename)
            
            # Docker files
            if filename in patterns['docker']:
                container_files.append({'path': filepath, 'type': 'docker'})
            
            # Kubernetes files (check content for kind: )
            elif filename.endswith(('.yaml', '.yml')):
                try:
                    with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                        if re.search(r'kind:\s*(Deployment|Service|Pod|ConfigMap|Secret)', content, re.IGNORECASE):
                            container_files.append({'path': filepath, 'type': 'kubernetes'})
                except:
                    pass
            
            # Helm files
            elif filename in patterns['helm']:
                container_files.append({'path': filepath, 'type': 'helm'})
            
            # Terraform files
            elif filename.endswith(('.tf', '.hcl')):
                container_files.append({'path': filepath, 'type': 'terraform'})
        
        # Check directories for kubernetes/k8s
        for dirname in dirs:
            if any(p in dirname.lower() for p in ['kubernetes', 'k8s', 'helm']):
                container_files.append({'path': os.path.join(root, dirname), 'type': 'kubernetes-dir'})
    
    return container_files

def create_enhanced_report(results, output_path):
    """Create an enhanced human-readable report."""
    report = []
    report.append("# Enhanced HIPAA Compliance Scan Report")
    report.append("=" * 50)
    report.append(f"**Scan Date:** {results['scan_info']['timestamp']}")
    report.append(f"**Directory:** {results['scan_info']['directory']}")
    report.append(f"**Scanner Version:** {results['scan_info']['scanner_version']}")
    report.append(f"**Files Scanned:** {results['stats']['files_scanned']}")
    report.append(f"**Total Issues:** {results['stats']['issues_found']}")
    report.append("")
    
    # Executive Summary
    report.append("## Executive Summary")
    report.append("This report provides a comprehensive analysis of your healthcare application's HIPAA compliance posture.")
    
    severity_counts = results['stats']['severity_counts']
    total_high = severity_counts.get('HIGH', 0)
    total_medium = severity_counts.get('MEDIUM', 0)
    total_low = severity_counts.get('LOW', 0)
    
    if total_high > 0:
        report.append(f"⚠️  **CRITICAL:** {total_high} high-severity issues require immediate attention.")
    if total_medium > 0:
        report.append(f"🔶 **WARNING:** {total_medium} medium-severity issues should be addressed soon.")
    if total_low > 0:
        report.append(f"ℹ️  **INFO:** {total_low} low-severity issues for review.")
    
    report.append("")
    
    # Risk Assessment
    risk_level = "LOW"
    if total_high > 5:
        risk_level = "CRITICAL"
    elif total_high > 0 or total_medium > 10:
        risk_level = "HIGH"
    elif total_medium > 0:
        risk_level = "MEDIUM"
    
    report.append("## Risk Assessment")
    report.append(f"**Overall Risk Level:** {risk_level}")
    report.append("")
    
    # Severity Distribution
    report.append("## Issue Severity Distribution")
    for severity, count in severity_counts.items():
        if count > 0:
            report.append(f"- **{severity}:** {count} issue(s)")
    report.append("")
    
    # Category Summary
    report.append("## Issues by Category")
    for category, count in sorted(results['stats']['categories'].items(), key=lambda x: x[1], reverse=True):
        category_name = category.replace('_', ' ').title()
        report.append(f"- **{category_name}:** {count} issue(s)")
    report.append("")
    
    # Technology Stack Analysis
    report.append("## Technology Stack Analysis")
    report.append("### File Types Detected")
    extensions = results['file_extensions']
    for ext, count in sorted(extensions.items(), key=lambda x: x[1], reverse=True)[:15]:
        report.append(f"- {ext}: {count} file(s)")
    report.append("")
    
    # Security Dependencies
    report.append("### Security Dependencies Analysis")
    security_deps = results['security_dependencies']
    if security_deps['security_packages_found']:
        report.append("**Security packages found:**")
        for package in security_deps['security_packages_found']:
            report.append(f"- {package['package']} in {os.path.basename(package['file'])}")
    else:
        report.append("⚠️  **No recognized security packages detected.** Consider adding security-focused libraries.")
    
    if security_deps.get('vulnerable_packages'):
        report.append("\n**Potentially vulnerable packages:**")
        for vuln in security_deps['vulnerable_packages']:
            report.append(f"- {vuln['pattern']} in {os.path.basename(vuln['file'])}")
    
    report.append("")
    
    # AWS Infrastructure Analysis
    report.append("### AWS Infrastructure")
    aws_infra = results['aws_infrastructure']
    infra_found = any(aws_infra.values())
    
    if infra_found:
        report.append("**Infrastructure as Code files detected:**")
        for infra_type, files in aws_infra.items():
            if files:
                report.append(f"- {infra_type.replace('_', ' ').title()}: {len(files)} file(s)")
    else:
        report.append("No Infrastructure as Code files detected.")
    report.append("")
    
    # Deepgram Integration Analysis
    report.append("### Deepgram Integration Analysis")
    deepgram_analysis = results['deepgram_analysis']
    if deepgram_analysis['integration_files']:
        report.append(f"**Deepgram integration detected in {len(deepgram_analysis['integration_files'])} file(s).**")
        report.append("**HIPAA Compliance Checklist for Deepgram:**")
        report.append("- ✅ Ensure Business Associate Agreement (BAA) is signed")
        report.append("- ✅ Use Deepgram Enhanced tier for HIPAA compliance")
        report.append("- ✅ Enable PII redaction features")
        report.append("- ✅ Use secure API endpoints (HTTPS)")
        report.append("- ✅ Implement proper access controls")
        
        if deepgram_analysis['potential_issues']:
            report.append("\n**Potential Deepgram configuration issues:**")
            for issue in deepgram_analysis['potential_issues']:
                report.append(f"- {issue['issue']}: {issue['recommendation']}")
    else:
        report.append("No Deepgram integration detected.")
    report.append("")
    
    # Configuration Security
    report.append("### Configuration Security")
    config_analysis = results['config_analysis']
    if config_analysis['env_files']:
        report.append(f"**Environment files found:** {len(config_analysis['env_files'])}")
    if config_analysis['sensitive_configs']:
        report.append(f"**Files with sensitive configuration:** {len(config_analysis['sensitive_configs'])}")
        report.append("⚠️  Review these files to ensure secrets are properly managed.")
    report.append("")
    
    # SSL/TLS Analysis
    report.append("### SSL/TLS Certificate Analysis")
    ssl_certs = results['ssl_certificates']
    if ssl_certs:
        report.append("**SSL certificate files found:**")
        for cert in ssl_certs:
            report.append(f"- {os.path.relpath(cert['path'], results['scan_info']['directory'])} ({cert['type']})")
        report.append("✅ Ensure certificates are properly secured and regularly rotated.")
    else:
        report.append("No SSL certificate files detected.")
    report.append("")
    
    # Containerization
    report.append("### Containerization")
    containers = results['containerization']
    if containers:
        container_types = {}
        for container in containers:
            container_type = container['type']
            if container_type not in container_types:
                container_types[container_type] = 0
            container_types[container_type] += 1
        
        report.append("**Container configuration files:**")
        for container_type, count in container_types.items():
            report.append(f"- {container_type.title()}: {count} file(s)")
        report.append("✅ Ensure container images are scanned for vulnerabilities.")
        report.append("✅ Use non-root users in containers.")
        report.append("✅ Implement proper secrets management.")
    else:
        report.append("No containerization files detected.")
    report.append("")
    
    # Priority Recommendations
    report.append("## Priority Recommendations")
    recommendations = results.get('recommendations', [])
    
    # Group by priority
    critical_recs = [r for r in recommendations if r['priority'] == 'CRITICAL']
    high_recs = [r for r in recommendations if r['priority'] == 'HIGH']
    medium_recs = [r for r in recommendations if r['priority'] == 'MEDIUM']
    
    if critical_recs:
        report.append("### 🚨 CRITICAL Priority")
        for rec in critical_recs:
            report.append(f"**{rec['category']}:** {rec['recommendation']}")
            report.append(f"*Details:* {rec['details']}")
            report.append("")
    
    if high_recs:
        report.append("### ⚠️  HIGH Priority")
        for rec in high_recs:
            report.append(f"**{rec['category']}:** {rec['recommendation']}")
            report.append(f"*Details:* {rec['details']}")
            report.append("")
    
    if medium_recs:
        report.append("### 🔶 MEDIUM Priority")
        for rec in medium_recs:
            report.append(f"**{rec['category']}:** {rec['recommendation']}")
            report.append(f"*Details:* {rec['details']}")
            report.append("")
    
    # Detailed Findings
    report.append("## Detailed Findings")
    
    for category, findings in results['findings'].items():
        if not findings:
            continue
            
        report.append(f"### {category.replace('_', ' ').title()}")
        
        # Add category description
        category_descriptions = {
            "weak_auth": "Authentication weaknesses that could allow unauthorized access to PHI.",
            "encryption_issues": "Encryption problems that could expose PHI in transit or at rest.",
            "data_exposure": "Code patterns that might inadvertently expose sensitive health information.",
            "phi_handling": "Areas where Protected Health Information (PHI) is being processed.",
            "audit_concerns": "Missing or inadequate audit logging for HIPAA compliance.",
            "session_issues": "Session management problems that could affect security.",
            "third_party": "Third-party integrations requiring Business Associate Agreements.",
            "data_retention": "Data retention and disposal practices for HIPAA compliance.",
            "phi_patterns": "Potential PHI data patterns requiring protection.",
            "aws_security": "AWS cloud security misconfigurations.",
            "api_security": "API security vulnerabilities.",
            "input_validation": "Input validation issues that could lead to security vulnerabilities.",
            "config_security": "Configuration security problems."
        }
        
        if category in category_descriptions:
            report.append(category_descriptions[category])
        report.append("")
        
        # Group findings by file and severity
        files_to_findings = defaultdict(list)
        for finding in findings:
            filepath = finding.get('filepath', '')
            files_to_findings[filepath].append(finding)
        
        # Sort files by number of issues (descending)
        sorted_files = sorted(files_to_findings.items(), 
                            key=lambda x: len(x[1]), reverse=True)
        
        for filepath, file_findings in sorted_files[:10]:  # Limit to top 10 files
            relative_path = os.path.relpath(filepath, results['scan_info']['directory'])
            report.append(f"**File: {relative_path}** ({len(file_findings)} issue(s))")
            
            # Sort findings by severity
            severity_order = {'HIGH': 0, 'MEDIUM': 1, 'LOW': 2}
            sorted_findings = sorted(file_findings, 
                                   key=lambda x: severity_order.get(x.get('severity', 'LOW'), 3))
            
            for finding in sorted_findings[:5]:  # Limit to top 5 findings per file
                severity = finding.get('severity', 'LOW')
                severity_icon = {'HIGH': '🚨', 'MEDIUM': '⚠️', 'LOW': 'ℹ️'}.get(severity, 'ℹ️')
                
                report.append(f"  {severity_icon} Line {finding.get('line', 'N/A')}: `{finding.get('match', 'N/A')}`")
                report.append(f"     Context: `{finding.get('text', 'N/A')[:100]}...`")
            
            if len(file_findings) > 5:
                report.append(f"     ... and {len(file_findings) - 5} more issue(s)")
            
            report.append("")
        
        if len(files_to_findings) > 10:
            report.append(f"... and {len(files_to_findings) - 10} more file(s) with issues")
            report.append("")
    
    # HIPAA Compliance Checklist
    report.append("## HIPAA Compliance Checklist")
    report.append("Use this checklist to ensure comprehensive HIPAA compliance:")
    report.append("")
    
    checklist_items = [
        "Administrative Safeguards",
        "  - Assign a Security Officer",
        "  - Conduct regular risk assessments", 
        "  - Implement workforce training programs",
        "  - Create incident response procedures",
        "  - Establish access management policies",
        "",
        "Physical Safeguards",
        "  - Secure physical access to systems",
        "  - Implement workstation security",
        "  - Control device and media access",
        "",
        "Technical Safeguards", 
        "  - Implement access controls (unique user IDs, MFA)",
        "  - Enable audit controls and logging",
        "  - Ensure data integrity protections",
        "  - Use encryption for data at rest and in transit",
        "  - Implement automatic logoff",
        "",
        "Business Associate Agreements",
        "  - Sign BAAs with all third-party vendors (including Deepgram)",
        "  - Regularly review and update BAAs",
        "  - Ensure vendors have proper safeguards",
        "",
        "Breach Notification",
        "  - Implement breach detection procedures",
        "  - Create notification templates and procedures",
        "  - Establish timeline compliance (60 days to HHS)",
        "",
        "Cloud Security (AWS Specific)",
        "  - Enable CloudTrail logging",
        "  - Configure proper IAM policies",
        "  - Use KMS for encryption key management",
        "  - Implement VPC security groups properly",
        "  - Enable GuardDuty for threat detection"
    ]
    
    for item in checklist_items:
        report.append(item)
    
    report.append("")
    report.append("## Next Steps")
    report.append("1. **Address Critical Issues:** Fix all high-severity security vulnerabilities immediately")
    report.append("2. **Review Third-party Integrations:** Ensure all BAAs are signed and current")
    report.append("3. **Implement Missing Security Controls:** Add authentication, encryption, and audit logging")
    report.append("4. **Regular Monitoring:** Set up continuous security monitoring and regular scans")
    report.append("5. **Staff Training:** Conduct HIPAA compliance training for all team members")
    report.append("6. **Documentation:** Document all security policies and procedures")
    report.append("")
    
    report.append("---")
    report.append("*This report was generated by the Enhanced HIPAA Compliance Scanner v2.0*")
    report.append(f"*For questions or support, review the latest HIPAA guidelines at https://www.hhs.gov/hipaa*")
    
    # Write report
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write('\n'.join(report))
    
    return output_path

def create_zip_report(results, output_dir):
    """Create enhanced zip report with multiple output formats."""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    zip_filename = os.path.join(output_dir, f"enhanced_hipaa_scan_report_{timestamp}.zip")
    
    with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
        # Write JSON results
        json_path = os.path.join(output_dir, "hipaa_scan_results.json")
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, default=str, ensure_ascii=False)
        zipf.write(json_path, os.path.basename(json_path))
        
        # Write markdown report
        report_path = os.path.join(output_dir, "hipaa_scan_report.md")
        create_enhanced_report(results, report_path)
        zipf.write(report_path, os.path.basename(report_path))
        
        # Write CSV summary for easy analysis
        csv_path = os.path.join(output_dir, "findings_summary.csv")
        create_csv_summary(results, csv_path)
        zipf.write(csv_path, os.path.basename(csv_path))
    
    # Clean up temporary files
    for temp_file in [json_path, report_path, csv_path]:
        if os.path.exists(temp_file):
            os.remove(temp_file)
    
    return zip_filename

def create_csv_summary(results, csv_path):
    """Create CSV summary of findings for analysis."""
    import csv
    
    with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['category', 'severity', 'file', 'line', 'issue', 'context']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        
        for category, findings in results['findings'].items():
            for finding in findings:
                writer.writerow({
                    'category': category,
                    'severity': finding.get('severity', 'LOW'),
                    'file': os.path.basename(finding.get('filepath', '')),
                    'line': finding.get('line', ''),
                    'issue': finding.get('match', ''),
                    'context': finding.get('text', '')[:100]
                })

def generate_hash(directory):
    """Generate a hash of the directory path for unique output identification."""
    return hashlib.md5(directory.encode()).hexdigest()[:8]

def main():
    parser = argparse.ArgumentParser(
        description='Enhanced HIPAA Compliance Scanner for Healthcare Applications',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s /path/to/project
  %(prog)s /path/to/project --exclude node_modules dist
  %(prog)s /path/to/project --output ./reports
  %(prog)s /path/to/project --config-only
        """
    )
    
    parser.add_argument('directory', help='Directory to scan for HIPAA compliance')
    parser.add_argument('--exclude', nargs='+', 
                       help='Directories to exclude from scan',
                       default=['.git', 'node_modules', 'venv', '.venv', '__pycache__', 
                               '.pytest_cache', 'dist', 'build', '.next', 'coverage'])
    parser.add_argument('--output', help='Output directory for reports', default='.')
    parser.add_argument('--config-only', action='store_true',
                       help='Only analyze configuration files')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Enable verbose logging')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    directory = os.path.abspath(args.directory)
    output_dir = os.path.abspath(args.output)
    
    if not os.path.exists(directory):
        logger.error(f"Directory does not exist: {directory}")
        return 1
    
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Scan the directory
    logger.info(f"Starting enhanced HIPAA compliance scan of {directory}")
    logger.info(f"Excluding directories: {', '.join(args.exclude)}")
    
    try:
        results = scan_directory(directory, args.exclude)
        
        # Create output files
        dir_hash = generate_hash(directory)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Enhanced report
        report_filename = f"enhanced_hipaa_report_{timestamp}_{dir_hash}.md"
        report_path = os.path.join(output_dir, report_filename)
        create_enhanced_report(results, report_path)
        logger.info(f"Enhanced report generated: {report_path}")
        
        # JSON output
        json_filename = f"hipaa_scan_results_{timestamp}_{dir_hash}.json"
        json_path = os.path.join(output_dir, json_filename)
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, default=str, ensure_ascii=False)
        logger.info(f"JSON results saved: {json_path}")
        
        # Create comprehensive zip report
        zip_path = create_zip_report(results, output_dir)
        logger.info(f"Comprehensive ZIP report generated: {zip_path}")
        
        # Print summary
        print(f"\n{'='*60}")
        print("ENHANCED HIPAA COMPLIANCE SCAN COMPLETE")
        print(f"{'='*60}")
        print(f"Directory Scanned: {directory}")
        print(f"Files Analyzed: {results['stats']['files_scanned']}")
        print(f"Total Issues Found: {results['stats']['issues_found']}")
        
        severity_counts = results['stats']['severity_counts']
        print(f"\nSeverity Breakdown:")
        print(f"  🚨 Critical/High: {severity_counts.get('HIGH', 0)}")
        print(f"  ⚠️  Medium: {severity_counts.get('MEDIUM', 0)}")
        print(f"  ℹ️  Low/Info: {severity_counts.get('LOW', 0)}")
        
        print(f"\nOutput Files:")
        print(f"  📄 Detailed Report: {report_path}")
        print(f"  📊 JSON Data: {json_path}")
        print(f"  📦 Complete Package: {zip_path}")
        
        print(f"\nTop Issue Categories:")
        sorted_categories = sorted(results['stats']['categories'].items(), 
                                 key=lambda x: x[1], reverse=True)
        for category, count in sorted_categories[:5]:
            category_name = category.replace('_', ' ').title()
            print(f"  • {category_name}: {count} issue(s)")
        
        # Priority recommendations
        recommendations = results.get('recommendations', [])
        critical_recs = [r for r in recommendations if r['priority'] == 'CRITICAL']
        high_recs = [r for r in recommendations if r['priority'] == 'HIGH']
        
        if critical_recs or high_recs:
            print(f"\n🚨 URGENT ACTION REQUIRED:")
            for rec in critical_recs[:3]:
                print(f"  • {rec['category']}: {rec['recommendation']}")
            for rec in high_recs[:2]:
                print(f"  • {rec['category']}: {rec['recommendation']}")
        
        print(f"\n📋 Next Steps:")
        print(f"  1. Review the detailed report: {os.path.basename(report_path)}")
        print(f"  2. Address critical security issues immediately")
        print(f"  3. Ensure Business Associate Agreements are in place")
        print(f"  4. Implement missing security controls")
        print(f"  5. Schedule regular compliance scans")
        
        return 0
        
    except Exception as e:
        logger.error(f"Scan failed: {e}")
        return 1

if __name__ == "__main__":
    exit(main())