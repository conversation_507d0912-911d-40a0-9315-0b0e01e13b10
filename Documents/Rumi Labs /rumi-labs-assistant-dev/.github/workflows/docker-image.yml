name: Dock<PERSON> Build, Push, and Deploy to ECS

on:
  push:
    branches:
      - dev  # Adjust as needed

jobs:
  build-push-deploy:
    runs-on: ubuntu-latest

    env:
      AWS_REGION: ${{ vars.AWS_REGION }}
      AWS_ACCOUNT_ID: ${{ vars.AWS_ACCOUNT_ID }}
      AWS_ECR_REPOSITORY: ${{ vars.AWS_ECR_REPOSITORY }}
      AWS_ACCESS_KEY_ID: ${{ vars.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ vars.AWS_SECRET_ACCESS_KEY }}
      GITHUB_RUN_NUMBER: ${{ github.run_number }}  # Unique build number for this run
      BRANCH_NAME: ${{ github.ref_name }}  # Get the branch name
      ECS_CLUSTER: ${{ vars.ECS_CLUSTER }}
      ECS_SERVICE: ${{ vars.ECS_SERVICE }}
      TASK_DEFINITION: ${{ vars.TASK_DEFINITION }}

      AWS_REGION_NAME: ${{ vars.AWS_REGION_NAME }}
      COGNITO_USER_POOL_ID: ${{ vars.COGNITO_USER_POOL_ID }}
      COGNITO_CLIENT_ID: ${{ vars.COGNITO_CLIENT_ID }}
      AWS_SERVER_PUBLIC_KEY: ${{ vars.AWS_SERVER_PUBLIC_KEY }}
      AWS_SERVER_SECRET_KEY: ${{ vars.AWS_SERVER_SECRET_KEY }}
      BEDROCK_REGION_NAME: ${{ vars.BEDROCK_REGION_NAME }}
      OPENAI_API_KEY: ${{ vars.OPENAI_API_KEY }}
      DEEPGRAM_API_KEY: ${{ vars.DEEPGRAM_API_KEY }}
      CLOUDWATCH_GROUP_NAME: ${{ vars.CLOUDWATCH_GROUP_NAME }}
      CLOUDWATCH_STREAM_NAME: ${{ vars.CLOUDWATCH_STREAM_NAME }}
      DB_HOST: ${{ vars.DB_HOST }}
      DB_USER: ${{ vars.DB_USER }}
      DB_PASSWORD: ${{ vars.DB_PASSWORD }}
      DB_NAME: ${{ vars.DB_NAME }}
      SITE_ID: ${{ vars.SITE_ID }}
      LOGGER_TYPE: ${{ vars.LOGGER_TYPE }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Set up Docker
        uses: docker/setup-buildx-action@v2

      - name: Log in to Amazon ECR
        run: |
          aws ecr get-login-password --region ${{ env.AWS_REGION }} | docker login --username AWS --password-stdin ${{ env.AWS_ACCOUNT_ID }}.dkr.ecr.${{ env.AWS_REGION }}.amazonaws.com

      - name: Build Docker image
        run: docker build -t backend .

      - name: Tag Docker image
        run: |
          docker tag backend:latest ${{ env.AWS_ACCOUNT_ID }}.dkr.ecr.${{ env.AWS_REGION }}.amazonaws.com/${{ env.BRANCH_NAME }}/${{ env.AWS_ECR_REPOSITORY }}:latest
          docker tag backend:latest ${{ env.AWS_ACCOUNT_ID }}.dkr.ecr.${{ env.AWS_REGION }}.amazonaws.com/${{ env.BRANCH_NAME }}/${{ env.AWS_ECR_REPOSITORY }}:${{ env.GITHUB_RUN_NUMBER }}

      - name: Push Docker image to ECR
        run: |
          docker push ${{ env.AWS_ACCOUNT_ID }}.dkr.ecr.${{ env.AWS_REGION }}.amazonaws.com/${{ env.BRANCH_NAME }}/${{ env.AWS_ECR_REPOSITORY }}:latest
          docker push ${{ env.AWS_ACCOUNT_ID }}.dkr.ecr.${{ env.AWS_REGION }}.amazonaws.com/${{ env.BRANCH_NAME }}/${{ env.AWS_ECR_REPOSITORY }}:${{ env.GITHUB_RUN_NUMBER }}

      - name: Generate Hash of Environment Variables
        id: generate-hash
        run: |
          echo "${{ env.AWS_REGION_NAME }}${{ env.COGNITO_USER_POOL_ID }}${{ env.COGNITO_CLIENT_ID }}${{ env.AWS_SERVER_PUBLIC_KEY }}${{ env.AWS_SERVER_SECRET_KEY }}${{ env.BEDROCK_REGION_NAME }}${{ env.OPENAI_API_KEY }}${{ env.DEEPGRAM_API_KEY }}${{ env.CLOUDWATCH_GROUP_NAME }}${{ env.CLOUDWATCH_STREAM_NAME }}${{ env.DB_HOST }}${{ env.DB_USER }}${{ env.DB_PASSWORD }}${{ env.DB_NAME }}${{ env.LOGGER_TYPE }}" | sha256sum | cut -d' ' -f1 > new_env_hash.txt

      - name: Create S3 Folder if Not Exists
        run: |
          aws s3api head-object --bucket ecs-env-hash-storage-backend --key "ecr-access/${{ env.BRANCH_NAME }}/env-hashes/" || aws s3api put-object --bucket ecs-env-hash-storage-backend --key "ecr-access/${{ env.BRANCH_NAME }}/env-hashes/"

      - name: Compare Environment Hashes
        id: compare-hash
        run: |
          aws s3 cp s3://ecs-env-hash-storage-backend/ecr-access/${{ env.BRANCH_NAME }}/env-hashes/previous_env_hash.txt previous_env_hash.txt || echo "No previous hash found."
          if diff -q previous_env_hash.txt new_env_hash.txt > /dev/null; then
            echo "Environment variables have not changed."
            echo "SKIP_TASK_DEFINITION_UPDATE=true" >> $GITHUB_ENV
          else
            echo "Environment variables have changed."
            echo "SKIP_TASK_DEFINITION_UPDATE=false" >> $GITHUB_ENV
            aws s3 cp new_env_hash.txt s3://ecs-env-hash-storage-backend/ecr-access/${{ env.BRANCH_NAME }}/env-hashes/previous_env_hash.txt
          fi

      - name: Update ECS Task Definition (Conditional)
        id: task-def-update
        if: env.SKIP_TASK_DEFINITION_UPDATE == 'false'
        run: |
          aws ecs describe-task-definition --task-definition ${{ env.TASK_DEFINITION }} > task_definition.json

          jq '.taskDefinition | del(.status, .revision, .taskDefinitionArn, .requiresAttributes, .compatibilities, .registeredAt, .registeredBy)' task_definition.json > task_definition_modified.json

          jq '.containerDefinitions[0].environment = [
            {"name": "AWS_REGION_NAME", "value": "${{ env.AWS_REGION_NAME }}"},
            {"name": "COGNITO_USER_POOL_ID", "value": "${{ env.COGNITO_USER_POOL_ID }}"},
            {"name": "COGNITO_CLIENT_ID", "value": "${{ env.COGNITO_CLIENT_ID }}"},
            {"name": "AWS_SERVER_PUBLIC_KEY", "value": "${{ env.AWS_SERVER_PUBLIC_KEY }}"},
            {"name": "AWS_SERVER_SECRET_KEY", "value": "${{ env.AWS_SERVER_SECRET_KEY }}"},
            {"name": "BEDROCK_REGION_NAME", "value": "${{ env.BEDROCK_REGION_NAME }}"},
            {"name": "OPENAI_API_KEY", "value": "${{ env.OPENAI_API_KEY }}"},
            {"name": "DEEPGRAM_API_KEY", "value": "${{ env.DEEPGRAM_API_KEY }}"},
            {"name": "CLOUDWATCH_GROUP_NAME", "value": "${{ env.CLOUDWATCH_GROUP_NAME }}"},
            {"name": "CLOUDWATCH_STREAM_NAME", "value": "${{ env.CLOUDWATCH_STREAM_NAME }}"},
            {"name": "DB_HOST", "value": "${{ env.DB_HOST }}"},
            {"name": "DB_USER", "value": "${{ env.DB_USER }}"},
            {"name": "DB_PASSWORD", "value": "${{ env.DB_PASSWORD }}"},
            {"name": "DB_NAME", "value": "${{ env.DB_NAME }}"},
            {"name": "SITE_ID", "value": "${{ env.SITE_ID }}"},
            {"name": "LOGGER_TYPE", "value": "${{ env.LOGGER_TYPE }}"}
          ]' task_definition_modified.json > task_definition_final.json

          aws ecs register-task-definition --cli-input-json file://task_definition_final.json

      - name: Get New Task Definition Revision
        id: get-task-revision
        run: |
          NEW_TASK_DEF_ARN=$(aws ecs describe-task-definition --task-definition ${{ env.TASK_DEFINITION }} --query "taskDefinition.taskDefinitionArn" --output text)
          echo "TASK_DEF_ARN=${NEW_TASK_DEF_ARN}" >> $GITHUB_ENV

      - name: Update ECS Service
        run: |
          aws ecs update-service \
            --cluster ${{ env.ECS_CLUSTER }} \
            --service ${{ env.ECS_SERVICE }} \
            --task-definition ${{ env.TASK_DEF_ARN }} \
            --force-new-deployment
