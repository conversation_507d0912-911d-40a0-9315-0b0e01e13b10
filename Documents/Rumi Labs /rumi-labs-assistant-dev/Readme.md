To Run App locally

1) Create Virtual Environment
    python -m venv venv
2) Start Virtual Environment 
    source venv/bin/activate # Linux
    ./venv/Script/activate.exe #Windows
3) Install Dependencies
    pip install -r requirements.txt

4) Create Migrations
    python manage.py makemigrations
    python manage.py migrate

5) Start Server
    python manage.py runserver


To Create a docker image

1) Go to project folder

2) Build Docker image from docker File
    docker build . -t image_name

3) Insert Environment Variables to docker image and Run image
    docker run -p 80:80 -e ENV_VAR_1=VALUE -e ENV_VAR_2=VALUE -e ENV_VAR_n=VALUE image_name
