# Generated by Django 5.0.7 on 2025-02-10 12:51

import django.contrib.auth.models
import django.contrib.auth.validators
import django.core.validators
import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='Organization',
            fields=[
                ('id', models.AutoField(auto_created=True, db_column='id', primary_key=True, serialize=False)),
                ('name', models.CharField(db_index=True, help_text="Organization's unique name", max_length=100, unique=True)),
                ('description', models.TextField(blank=True, help_text='Detailed description of the organization', null=True)),
                ('address', models.TextField(blank=True, help_text='Physical address of the organization', null=True)),
                ('number_of_employees', models.PositiveIntegerField(default=0, help_text='Total number of employees in the organization', validators=[django.core.validators.MinValueValidator(0)])),
                ('users_limit', models.PositiveIntegerField(default=1000, help_text='Maximum number of users allowed', validators=[django.core.validators.MinValueValidator(1)])),
                ('apis_limit', models.PositiveIntegerField(default=1000, help_text='Maximum number of API calls allowed', validators=[django.core.validators.MinValueValidator(1)])),
                ('users_count', models.PositiveIntegerField(default=0, help_text='Current number of users', validators=[django.core.validators.MinValueValidator(0)])),
                ('apis_count', models.PositiveIntegerField(default=0, help_text='Current number of API calls', validators=[django.core.validators.MinValueValidator(0)])),
                ('bucket_name', models.CharField(blank=True, default=None, help_text='AWS S3 bucket name for organization storage', max_length=1000, null=True)),
                ('log_group_name', models.CharField(blank=True, default=None, help_text='AWS CloudWatch log group name', max_length=1000, null=True)),
                ('ready', models.BooleanField(default=False, help_text='Indicates if the organization is fully configured and ready')),
            ],
            options={
                'verbose_name': 'Organization',
                'verbose_name_plural': 'Organizations',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='ClinicUser',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.CharField(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.UnicodeUsernameValidator()], verbose_name='username')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='email address')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('auth_provider', models.CharField(choices=[('email', 'Email'), ('google', 'Google'), ('microsoft', 'Microsoft')], default='email', help_text='Authentication method used by the user', max_length=255)),
                ('email_verified', models.BooleanField(default=False, help_text="Indicates if user's email has been verified")),
                ('is_admin', models.BooleanField(default=True, help_text='Indicates if user has administrative privileges')),
                ('is_doctor', models.BooleanField(default=True, help_text='Indicates if user is a medical doctor')),
                ('clinic', models.CharField(blank=True, help_text='Name or identifier of the specific clinic', max_length=500)),
                ('department', models.CharField(blank=True, help_text='Department within the clinic', max_length=255)),
                ('room_number', models.PositiveIntegerField(blank=True, help_text='Room number assigned to the user', null=True)),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
                ('organization', models.ForeignKey(help_text='Associated healthcare organization', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='users', to='accounts.organization')),
            ],
            options={
                'verbose_name': 'Clinic User',
                'verbose_name_plural': 'Clinic Users',
                'ordering': ['username'],
            },
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
    ]
