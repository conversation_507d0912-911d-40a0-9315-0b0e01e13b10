# Generated by Django 5.0.7 on 2025-03-16 09:59

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0001_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='organization',
            name='address',
            field=models.TextField(blank=True, default='', help_text='Physical address of the organization'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='organization',
            name='bucket_name',
            field=models.CharField(blank=True, default=None, help_text='AWS S3 bucket name for organization storage', max_length=1000),
        ),
        migrations.AlterField(
            model_name='organization',
            name='description',
            field=models.TextField(blank=True, default='', help_text='Detailed description of the organization'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='organization',
            name='log_group_name',
            field=models.CharField(blank=True, default=None, help_text='AWS CloudWatch log group name', max_length=1000),
        ),
    ]
