from rest_framework.permissions import BasePermission


class IsAdminOnly(BasePermission):
    """
    Permission class to check if the user is an authenticated admin.
    """
    def has_permission(self, request, view):
        return bool(
            request.user 
            and request.user.is_authenticated 
            and request.user.is_admin
        )


class IsDoctorOnly(BasePermission):
    """
    Permission class to check if the user is an authenticated doctor.
    """
    def has_permission(self, request, view):
        return bool(
            request.user 
            and request.user.is_authenticated 
            and request.user.is_doctor
        )


class IsSuperUserOnly(BasePermission):
    """
    Permission class to check if the user is an authenticated superuser.
    """
    def has_permission(self, request, view):
        return bool(
            request.user 
            and request.user.is_authenticated 
            and request.user.is_superuser
        )