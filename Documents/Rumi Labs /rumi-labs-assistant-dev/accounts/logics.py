import logging
from django.conf import settings
from project_helper.aws_helper import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>
from allauth.socialaccount.models import SocialToken, SocialAccount
import requests
s3_helper = S3Helper()
logger = logging.getLogger(__name__)


def get_google_access_token(user):
    try:
        social_account = SocialAccount.objects.get(user=user, provider='google')
        token = SocialToken.objects.get(account=social_account)
        return token.token
    except (SocialAccount.DoesNotExist, SocialToken.DoesNotExist):
        return None


def get_google_calender_events_by_date_range(access_token, start_date, end_date, max_results=10):

    url = "https://www.googleapis.com/calendar/v3/calendars/primary/events"

    params = {
        "timeMin": start_date,
        "timeMax": end_date,
        "maxResults": max_results,
        "orderBy": "startTime",
        "singleEvents": True,
    }

    headers = {"Authorization": f"Bearer {access_token}"}
    response = requests.get(url, headers=headers, params=params)

    return response


class OrganizationAWSManager:
    """Manages AWS resources for organizations."""
    
    def __init__(self, region_name: str = settings.AWS_REGION_NAME):
        self.aws_helper = AWSHelper(region_name=region_name)
        self.cloudwatch_client = self.aws_helper.cloudwatch_client

    def create_storage_bucket(self, organization) -> bool:
        """
        Creates an S3 bucket for the organization.
        
        Args:
            organization: Organization model instance
            
        Returns:
            bool: True if successful, False otherwise
            
        Raises:
            ValueError: If organization name is invalid
        """
        if not organization.name:
            raise ValueError("Organization name cannot be empty")
            
        try:
            bucket_name = self._sanitize_bucket_name(organization.name)
            s3_helper.create_bucket(bucket_name)
            organization.bucket_name = bucket_name
            organization.save()
            logger.info(f"Created S3 bucket '{bucket_name}' for organization {organization.id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create S3 bucket for organization {organization.id}: {str(e)}")
            raise

    def create_log_group_and_streams(self, organization) -> bool:
        """
        Creates CloudWatch log group and streams for the organization.
        
        Args:
            organization: Organization model instance
            
        Returns:
            bool: True if successful, False otherwise
        """
        log_group_name = organization.name
        streams = {
            "general": "General logs",
            "critical_apis": "Critical API logs"
        }

        try:
            # Create log group
            try:
                self.cloudwatch_client.create_log_group(logGroupName=log_group_name)
                organization.log_group_name = log_group_name
                organization.save()
                logger.info(f"Created log group '{log_group_name}' for organization {organization.id}")
            except self.cloudwatch_client.exceptions.ResourceAlreadyExistsException:
                logger.debug(f"Log group '{log_group_name}' already exists")

            # Create log streams
            for stream_name in streams:
                try:
                    self.cloudwatch_client.create_log_stream(
                        logGroupName=log_group_name,
                        logStreamName=stream_name
                    )
                    logger.info(f"Created log stream '{stream_name}' in group '{log_group_name}'")
                except self.cloudwatch_client.exceptions.ResourceAlreadyExistsException:
                    logger.debug(f"Log stream '{stream_name}' already exists")
            
            return True

        except Exception as e:
            logger.error(f"Failed to create CloudWatch resources for organization {organization.id}: {str(e)}")
            raise

    @staticmethod
    def _sanitize_bucket_name(name: str) -> str:
        """
        Sanitizes the organization name for use as an S3 bucket name.
        
        Args:
            name: Original organization name
            
        Returns:
            str: Sanitized bucket name
        """
        return str(name).lower().replace(' ', '-')


# Initialize the AWS manager
aws_manager = OrganizationAWSManager()