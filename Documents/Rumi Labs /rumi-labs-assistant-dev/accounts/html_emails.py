password_reset = """<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Reset</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            margin: 0;
            padding: 0;
        }
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
        }
        h2 {
            color: #333333;
            font-size: 24px;
            margin-bottom: 20px;
        }
        p {
            color: #666666;
            line-height: 1.6;
            font-size: 16px;
        }
        .button-container {
            text-align: center;
            margin: 30px 0;
        }
        .reset-button {
            background-color: #4CAF50;
            color: white;
            padding: 12px 24px;
            border-radius: 5px;
            text-decoration: none;
            font-size: 18px;
            display: inline-block;
            transition: background-color 0.3s;
        }
        .reset-button:hover {
            background-color: #e64a19;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <h2>Password Reset Request</h2>
        <p>Hello,</p>
        <p>We received a request to reset your password. Click the button below to proceed:</p>
        <div class="button-container">
            <a href="<<reset_link>>" class="reset-button">Reset Password</a>
        </div>
        <p>If you did not request this, you can safely ignore this email.</p>
        <p>Thank you,<br>RumiLabs.ai</p>
    </div>
</body>
</html>"""



account_confirmation = """
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Confirmation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            margin: 0;
            padding: 0;
        }
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
        }
        h2 {
            color: #333333;
            font-size: 24px;
            margin-bottom: 20px;
        }
        p {
            color: #666666;
            line-height: 1.6;
            font-size: 16px;
        }
        .button-container {
            text-align: center;
            margin: 30px 0;
        }
        .confirm-button {
            background-color: #4CAF50;
            color: white;
            padding: 12px 24px;
            border-radius: 5px;
            text-decoration: none;
            font-size: 18px;
            display: inline-block;
            transition: background-color 0.3s;
        }
        .confirm-button:hover {
            background-color: #0069d9;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <h2>Confirm Your Account</h2>
        <p>Hello,</p>
        <p>Thank you for registering with us! Please click the button below to confirm your account:</p>
        <div class="button-container">
            <a href="<<confirmation_link>>" class="confirm-button">Confirm Account</a>
        </div>
        <p>If you did not create an account with us, you can safely ignore this email.</p>
        <p>Thank you,<br>RumiLabs.ai</p>
    </div>
</body>
</html>
"""