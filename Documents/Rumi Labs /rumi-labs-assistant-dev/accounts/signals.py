# signals.py
from social_django.models import UserSocialAuth
from django.dispatch import receiver
from django.db.models.signals import post_save

@receiver(post_save, sender=UserSocialAuth)
def link_user(sender, instance, created, **kwargs):
    if created:
        user = instance.user
        if instance.provider == 'google':
            user.auth_provider = 'google'
        elif instance.provider == 'azuread':
            user.auth_provider = 'microsoft'
        user.save()
