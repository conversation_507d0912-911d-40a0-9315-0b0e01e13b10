from django.contrib.auth.models import AbstractUser
from django.db import models
from django.core.validators import MinValueValidator
from django.core.exceptions import ValidationError

# Create your models here.

class Organization(models.Model):
    """
    Represents a healthcare organization/clinic with resource limits and configuration.
    
    This model tracks organization details, resource usage, and AWS-related configurations.
    """
    # Basic Information
    id = models.AutoField(primary_key=True, db_column="id", auto_created=True)
    name = models.CharField(
        max_length=100, 
        unique=True, 
        db_index=True,
        help_text="Organization's unique name"
    )
    description = models.TextField(
        blank=True,
        help_text="Detailed description of the organization"
    )
    address = models.TextField(
        blank=True,
        help_text="Physical address of the organization"
    )

    # Resource Limits and Usage
    number_of_employees = models.PositiveIntegerField(
        default=0,
        validators=[MinValueValidator(0)],
        help_text="Total number of employees in the organization"
    )
    users_limit = models.PositiveIntegerField(
        default=1000,
        validators=[MinValueValidator(1)],
        help_text="Maximum number of users allowed"
    )
    apis_limit = models.PositiveIntegerField(
        default=1000,
        validators=[MinValueValidator(1)],
        help_text="Maximum number of API calls allowed"
    )
    users_count = models.PositiveIntegerField(
        default=0,
        validators=[MinValueValidator(0)],
        help_text="Current number of users"
    )
    apis_count = models.PositiveIntegerField(
        default=0,
        validators=[MinValueValidator(0)],
        help_text="Current number of API calls"
    )

    # AWS Configuration
    bucket_name = models.CharField(
        max_length=1000,
        blank=True,
        default="",
        help_text="AWS S3 bucket name for organization storage"
    )
    log_group_name = models.CharField(
        max_length=1000,
        blank=True,
        default="",
        help_text="AWS CloudWatch log group name"
    )
    ready = models.BooleanField(
        default=False,
        help_text="Indicates if the organization is fully configured and ready"
    )

    class Meta:
        verbose_name = "Organization"
        verbose_name_plural = "Organizations"
        ordering = ['name']

    def __str__(self):
        return self.name

    def clean(self):
        """Validate organization constraints."""
        if self.users_count > self.users_limit:
            raise ValidationError("Users count cannot exceed users limit")
        if self.apis_count > self.apis_limit:
            raise ValidationError("API count cannot exceed API limit")

    def has_available_user_slots(self):
        """Check if organization can accommodate more users."""
        return self.users_count < self.users_limit

    def has_available_api_slots(self):
        """Check if organization can make more API calls."""
        return self.apis_count < self.apis_limit


class ClinicUser(AbstractUser):
    """
    Extended user model for clinic staff members.
    
    Includes additional fields for authentication, role management, and clinic-specific information.
    """
    # Authentication
    AUTH_PROVIDERS = [
        ('email', 'Email'),
        ('google', 'Google'),
        ('microsoft', 'Microsoft')
    ]
    
    auth_provider = models.CharField(
        max_length=255,
        choices=AUTH_PROVIDERS,
        default='email',
        help_text="Authentication method used by the user"
    )
    email_verified = models.BooleanField(
        default=False,
        help_text="Indicates if user's email has been verified"
    )

    # Role Information
    is_admin = models.BooleanField(
        default=True,
        help_text="Indicates if user has administrative privileges"
    )
    is_doctor = models.BooleanField(
        default=True,
        help_text="Indicates if user is a medical doctor"
    )

    # Clinic Information
    organization = models.ForeignKey(
        Organization,
        on_delete=models.SET_NULL,
        null=True,
        related_name='users',
        help_text="Associated healthcare organization"
    )
    clinic = models.CharField(
        max_length=500,
        blank=True,
        help_text="Name or identifier of the specific clinic"
    )
    department = models.CharField(
        max_length=255,
        blank=True,
        help_text="Department within the clinic"
    )
    room_number = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text="Room number assigned to the user"
    )

    class Meta:
        verbose_name = "Clinic User"
        verbose_name_plural = "Clinic Users"
        ordering = ['username']

    def __str__(self):
        return f"{self.username} ({self.get_full_name()})"

    def clean(self):
        """Validate user constraints."""
        if self.organization and not self.organization.has_available_user_slots():
            raise ValidationError("Organization has reached its user limit")

    @property
    def full_name(self):
        """Return the user's full name or username if not available."""
        return self.get_full_name() or self.username

    @property
    def role_display(self):
        """Return a display string for the user's role."""
        roles = []
        if self.is_admin:
            roles.append("Admin")
        if self.is_doctor:
            roles.append("Doctor")
        return ", ".join(roles) or ""

