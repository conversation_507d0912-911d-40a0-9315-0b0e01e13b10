# accounts/serializers.py

from rest_framework import serializers
from .models import Organization, ClinicUser

class OrganizationSerializer(serializers.ModelSerializer):
    """Serializer for Organization model.
    
    Handles the serialization of organization data including name, employee count,
    address, description, and various limits/counts.
    """
    
    class Meta:
        model = Organization
        fields = [
            'id',
            'name',
            'number_of_employees',
            'address',
            'description',
            'users_limit',
            'apis_limit',
            'apis_count',
            'users_count'
        ]
        read_only_fields = ['id', 'apis_count', 'users_count']


class ClinicUserSerializer(serializers.ModelSerializer):
    """Serializer for ClinicUser model.
    
    Handles the serialization of clinic user data including personal information,
    organization details, and clinic-specific information.
    """
    
    class Meta:
        model = ClinicUser
        fields = [
            'id',
            'username',
            'email',
            'first_name',
            'last_name',
            'organization',
            'date_joined',
            'is_admin',
            'is_doctor',
            'clinic',
            'department',
            'room_number'
        ]
        read_only_fields = ['id', 'date_joined']


class LoginClinicUserSerializer(serializers.ModelSerializer):
    """Serializer for ClinicUser login response.
    
    Provides essential user information needed after successful login,
    including organization details and user permissions.
    """
    
    organization_id = serializers.SerializerMethodField(
        help_text="The ID of the organization the user belongs to"
    )

    class Meta:
        model = ClinicUser
        fields = [
            'id',
            'username',
            'email',
            'is_admin',
            'is_doctor',
            'is_superuser',
            'email_verified',
            'organization_id'
        ]
        read_only_fields = ['id', 'organization_id']

    def get_organization_id(self, obj: ClinicUser) -> int | None:
        """Retrieve the organization ID for the user.
        
        Args:
            obj: The ClinicUser instance being serialized.
            
        Returns:
            The organization ID if the user belongs to an organization, None otherwise.
        """
        return obj.organization.id if obj.organization else None