# accounts/views.py
import uuid
from django.core.mail import EmailMultiAlternatives
import django.db.transaction
from django.core.exceptions import BadRequest
from rest_framework import generics, status
from rest_framework.exceptions import AuthenticationFailed, PermissionDenied
from rest_framework.request import Request
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView
from rest_framework_simplejwt.tokens import RefreshToken, AccessToken
from rest_framework.response import Response
from rest_framework.views import APIView
from django.contrib.auth import get_user_model
from django.core.mail import send_mail
import jwt
import datetime
from allauth.socialaccount.providers.google.views import GoogleOAuth2Adapter
from dj_rest_auth.registration.views import SocialLoginView
from health_care.logics import TemplateManager
from health_care.models import DoctorInformation
from health_care.serializers import DoctorInformationSerializer
from .logics import aws_manager, get_google_access_token, get_google_calender_events_by_date_range
from .models import Organization, ClinicUser
from .serializers import <PERSON>ginClinic<PERSON>serSerializer, OrganizationSerializer, ClinicUserSerializer
from rest_framework.permissions import AllowAny
from .permissions import IsSuperUserOnly, IsAdminOnly, IsDoctorOnly
from.html_emails import password_reset, account_confirmation
from django.conf import settings
from project_helper.response_mixins import ResponseMixin
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

User = get_user_model()


class OrganizationCreationPipeLine(ResponseMixin, generics.CreateAPIView):
    serializer_class = OrganizationSerializer
    queryset = Organization.objects.all()
    permission_classes = [IsAuthenticated, IsSuperUserOnly | IsAdminOnly]
    
    def create(self, request, *args, **kwargs):
        if request.user.organization:
            return self.bad_request_response("Organization already exists for user")

        try:
            with django.db.transaction.atomic():
                if not request.user.is_superuser and request.user.organization:
                    return self.bad_request_response("Organization Already Exists for user")

                # Generate organization name
                request.data["name"] = f'{request.data.get("name", "")}.{uuid.uuid4()}' if request.data.get("name",
                                                                                                            None) else f"{uuid.uuid4()}"

                response = super().create(request, *args, **kwargs)
                organization = Organization.objects.get(id=response.data["id"])

                # Setup organization resources
                aws_manager.create_log_group_and_streams(organization)
                aws_manager.create_storage_bucket(organization)
                template_manager = TemplateManager()
                template_manager.add_default_templates_to_organization(organization)
                if organization.bucket_name and organization.log_group_name:
                    organization.ready = True
                    organization.save()

                request.user.organization = organization
                request.user.save()

                return self.success_created_response(data=response.data)
        except Exception as e:
            return self.internal_server_error_response(str(e))


class S3BucketCreateView(ResponseMixin, APIView):

    def post(self, request, *args, **kwargs):
        try:
            organization = self.request.user.organization
            aws_manager.create_storage_bucket_for_organization(organization)
            if organization.bucket_name and organization.log_group_name:
                organization.ready = True
                organization.save()
            return self.success_response(message="Bucket created successfully")
        except Exception as e:
            return self.bad_request_response(str(e))


class LogGroupCreateView(ResponseMixin, APIView):
    def post(self, request, *args, **kwargs):
        try:
            organization = self.request.user.organization
            aws_manager.create_log_group_and_streams_for_organization(organization)
            if organization.bucket_name and organization.log_group_name:
                organization.ready = True
                organization.save()
            return self.success_response(message="Log group created successfully")
        except Exception as e:
            return self.bad_request_response(str(e))


class RetrieveUpdateDeleteOrganizationView(ResponseMixin, generics.RetrieveUpdateDestroyAPIView):
    permission_classes = [IsSuperUserOnly | IsAdminOnly]
    serializer_class = OrganizationSerializer

    def get_queryset(self):
        if not self.request.user.is_authenticated:
            return None
        if self.request.user.is_superuser:
            return Organization.objects.filter(id=self.kwargs.get("pk"))
        else:
            return Organization.objects.filter(id=self.request.user.organization.id)

    def delete(self, request, *args, **kwargs):
        try:
            if not self.request.user.is_superuser:
                return self.forbidden_response("You are not authorized to delete this organization")
            organization_name = self.kwargs.get(self.lookup_field)
            organization = Organization.objects.get(name=organization_name)
            if organization is None:
                return self.bad_request_response("Organization not found")
            organization.delete()
            return self.success_response(message="Organization deleted successfully")
        except Exception as e:
            return self.internal_server_error_response(str(e))


class LoginViewV2(ResponseMixin, TokenObtainPairView):
    @swagger_auto_schema(
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'username': openapi.Schema(type=openapi.TYPE_STRING),
                'password': openapi.Schema(type=openapi.TYPE_STRING),
            },
            required=['username', 'password'],
        ),
        responses={
            200: openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'auth_info': openapi.Schema(type=openapi.TYPE_OBJECT,
                                                properties={
                                                    'refresh': openapi.Schema(type=openapi.TYPE_STRING),
                                                    'access': openapi.Schema(type=openapi.TYPE_STRING),
                                                }),
                    'user': openapi.Schema(type=openapi.TYPE_OBJECT,
                                                properties={
                                                    "id": openapi.Schema(type=openapi.TYPE_INTEGER),
                                                    "username": openapi.Schema(type=openapi.TYPE_STRING),
                                                    "email": openapi.Schema(type=openapi.FORMAT_EMAIL),
                                                    "is_admin": openapi.Schema(type=openapi.TYPE_BOOLEAN),
                                                    "is_doctor": openapi.Schema(type=openapi.TYPE_BOOLEAN),
                                                    "is_superuser": openapi.Schema(type=openapi.TYPE_BOOLEAN),
                                                    "email_verified": openapi.Schema(type=openapi.TYPE_BOOLEAN),
                                                    "organization_id": openapi.Schema(type=openapi.TYPE_INTEGER)
                                                }),
                    'doctor_data': openapi.Schema(type=openapi.TYPE_OBJECT,
                                                properties={
                                                    "id": openapi.Schema(type=openapi.TYPE_INTEGER),
                                                    "doctor": openapi.Schema(type=openapi.TYPE_INTEGER),
                                                    "speciality": openapi.Schema(type=openapi.TYPE_INTEGER),
                                                    "sub_speciality": openapi.Schema(type=openapi.TYPE_INTEGER),
                                                    "output_style": openapi.Schema(type=openapi.TYPE_INTEGER),
                                                    "selected_template": openapi.Schema(type=openapi.TYPE_INTEGER)
                                                }),
                }
            ),
            400: openapi.Response("Bad Request"),
        }
    )
    def post(self, request, *args, **kwargs):
        try:
            response = super().post(request, *args, **kwargs)
            user = User.objects.get(username=request.data["username"])

            if not user.email_verified:
                return self.bad_request_response("User is not verified. Please verify your account first")

            doctor_data = {}
            if user.is_doctor:
                doctor = DoctorInformation.objects.filter(doctor=user).first()
                doctor_data = DoctorInformationSerializer(doctor).data

            user_data = LoginClinicUserSerializer(user).data

            response_data = {
                "auth_info": response.data,
                "user": user_data,
                "doctor_data": doctor_data
            }

            return self.success_response(
                data=response_data,
                message="Login successful"
            )
        except AuthenticationFailed as e:
            return self.bad_request_response("Incorrect User Name or Password!!")
        except Exception as e:
            return self.internal_server_error_response(str(e))


class RefreshTokenView(TokenRefreshView):
    permission_classes = [AllowAny]

    def post(self, request: Request, *args, **kwargs) -> Response:
        response = super().post(request, *args, **kwargs)
        response.data["access_token_life_minutes"] = settings.ACCESS_TOKEN_LIFETIME_MINUTES
        return response


class LogoutViewV2(ResponseMixin, APIView):
    def post(self, request):
        try:
            refresh_token = request.data["refresh"]
            token = RefreshToken(refresh_token)
            token.blacklist()
            return self.success_response(message="Logged out successfully")
        except Exception as e:
            return self.bad_request_response(str(e))


class GetProfileView(ResponseMixin, APIView):
    def get(self, request):
        token = request.headers.get("Authorization")
        if not token:
            return self.bad_request_response("Token is required.")
        try:
            payload = jwt.decode(token, settings.SECRET_KEY, algorithms=["HS256"])
            email = payload["email"]
            user = User.objects.get(email=email)
            return self.success_response(data={"user": user})
        except jwt.ExpiredSignatureError:
            return self.bad_request_response("Verification token has expired.")
        except jwt.InvalidTokenError:
            return self.bad_request_response("Invalid token.")


class UpdatePasswordView(ResponseMixin, APIView):
    permission_classes = [IsAuthenticated]
    @swagger_auto_schema(
            request_body=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'current_password': openapi.Schema(type=openapi.TYPE_STRING, description='Current password of the user'),
                    'new_password': openapi.Schema(type=openapi.TYPE_STRING, description='New password to be set'),
                    'confirm_password': openapi.Schema(type=openapi.TYPE_STRING, description='Confirmation of the new password'),
                },
                required=['current_password', 'new_password', 'confirm_password']
            ),
            responses={
                200: openapi.Response('Password updated successfully.'),
                400: openapi.Response('Bad request.'),
            }
        )
    def post(self, request):
        user = request.user
        current_password = request.data.get('current_password')
        new_password = request.data.get('new_password')
        confirm_password = request.data.get('confirm_password')

        if not user.check_password(current_password):
            return self.bad_request_response("Current password is incorrect.")

        if new_password != confirm_password:
            return self.bad_request_response("New passwords do not match.")
        user.set_password(new_password)
        user.save()

        return self.success_response(message="Password updated successfully.")


class SignupView(ResponseMixin, APIView):
    permission_classes = [AllowAny]
    @swagger_auto_schema(
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'email': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_EMAIL, description='User email'),
                'username': openapi.Schema(type=openapi.TYPE_STRING, description='Username for the new user'),
                'password': openapi.Schema(type=openapi.TYPE_STRING, description='Password for the new user'),
            },
            required=['email', 'username', 'password']
        ),
        responses={
            201: openapi.Response('User registered successfully. Please check your email for verification.'),
            400: openapi.Response('Email is already registered.'),
            500: openapi.Response('Internal server error.'),
        }
    )
    def post(self, request):
        try:
            data = request.data
            email = data.get("email")
            username = data.get("username")
            password = data.get("password")

            if User.objects.filter(email=email).exists():
                return self.bad_request_response("Email is already registered.")

            user = User.objects.create_user(
                username=username,
                email=email,
                password=password
            )
            user.email_verified = False
            user.is_admin = True
            user.is_doctor = True
            user.save()
            # Generate verification token
            token = jwt.encode(
                {
                    "email": user.email,
                    "exp": datetime.datetime.now(datetime.UTC) + datetime.timedelta(hours=24)
                },
                settings.SECRET_KEY,
                algorithm="HS256"
            )

            verification_link = f"{settings.FRONTEND_URL}/verify-email?token={token}"
            message = account_confirmation.replace("<<confirmation_link>>",verification_link)
            # Send verification email
            send_mail(
                subject="Verify your email",
                message="",
                html_message=message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[email],
            )

            return self.success_created_response(
                message="User registered successfully. Please check your email for verification"
            )
        except Exception as e:
            return self.internal_server_error_response(str(e))


class ResendEmailVIew(ResponseMixin, APIView):
    @swagger_auto_schema(
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'email': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_EMAIL, description='Email to resend verification'),
            },
            required=['email']
        ),
        responses={
            200: openapi.Response('Verification Email Sent successfully'),
            400: openapi.Response('Email is required.'),
            500: openapi.Response('Internal server error.'),
        }
    )
    def post(self, request):
        email = request.data.get("email", None)
        if not email:
            return self.bad_request_response("Email is required.")
        token = jwt.encode(
            {"email": email, "exp": datetime.datetime.now(datetime.UTC) + datetime.timedelta(hours=24)},
            settings.SECRET_KEY,
            algorithm="HS256"
        )

        verification_link = f"{settings.FRONTEND_URL}/verify-email?token={token}"

        # Send verification email
        subject = "Account Confirmation Email"
        from_email = settings.DEFAULT_FROM_EMAIL

        # Replace the placeholder with the actual reset link
        html_content = account_confirmation.replace("<<confirmation_link>>",verification_link)

        # Create the email
        email_message = EmailMultiAlternatives(
            subject=subject,
            body="Click the link below to reset your password.",  # Plain-text alternative
            from_email=from_email,
            to=[email]
        )
        # Attach the HTML version
        email_message.attach_alternative(html_content, "text/html")
        email_message.send()

        return self.success_response(message="Verification Email Sent successfully")


class EmailVerifyView(ResponseMixin, APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        token = request.GET.get("token")

        try:
            payload = jwt.decode(token, settings.SECRET_KEY, algorithms=["HS256"])
            email = payload["email"]
            user = User.objects.get(email=email)

            if user.email_verified:
                return self.success_response(message="Email is already verified.")

            user.email_verified = True
            user.save()

            return self.success_response(message="Email verified successfully.")

        except jwt.ExpiredSignatureError:
            return self.bad_request_response("Verification token has expired.")
        except jwt.InvalidTokenError:
            return self.bad_request_response("Invalid token.")


class RetrieveUpdateDeleteUserView(ResponseMixin, generics.RetrieveUpdateDestroyAPIView):
    permission_classes = [IsSuperUserOnly | IsAdminOnly | IsDoctorOnly]
    serializer_class = ClinicUserSerializer
    queryset = ClinicUser.objects.all()
    def get_queryset(self):
        if not self.request.user.is_authenticated:
            return None
        if self.request.user.is_superuser:
            return ClinicUser.objects.filter(id=self.kwargs.get("pk"))
        else:
            if str(self.request.user.id) != str(self.kwargs.get("pk")):
                raise PermissionDenied("You can only access your own record.")
            user = ClinicUser.objects.filter(id=self.request.user.id)
            return user
    def delete(self, request, *args, **kwargs):
        if not request.user.is_superuser or not request.user.is_admin:
            return self.forbidden_response("You do not have permissions for this operation")

        return super().delete(request, *args, **kwargs)


class ListOrganizationUsers(generics.ListAPIView):
    permission_classes = [IsSuperUserOnly, IsAdminOnly]
    serializer_class = ClinicUserSerializer
    lookup_field = "org"

    def get_queryset(self):
        if not self.request.user.is_authenticated:
            return None
        if self.request.user.is_superuser:
            return ClinicUser.objects.filter(organization_id=self.kwargs.get("org"))
        else:
            return ClinicUser.objects.filter(organization=self.request.user.organization)


class HealthCheckView(ResponseMixin, APIView):
    permission_classes = [AllowAny]

    def get(self, request, *args, **kwargs):
        return self.success_response(message="Server Working!!")


class VerifyTokenView(ResponseMixin, APIView):
    permission_classes = [AllowAny]
    @swagger_auto_schema(
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'token': openapi.Schema(type=openapi.TYPE_STRING, description='Token to verify'),
            },
            required=['token']
        ),
        responses={
            200: openapi.Response('Token is valid', 
                                  schema=openapi.Schema(type=openapi.TYPE_OBJECT, 
                                                        properties={'valid': openapi.Schema(type=openapi.TYPE_BOOLEAN)})),
            400: openapi.Response(
                'Bad Request',
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'error': openapi.Schema(
                            type=openapi.TYPE_STRING,
                            description="Error message: 'Invalid Token' or 'Token is required'"
                        ),
                    },
                    example={'error': 'Invalid Token'},  # Example response
                    examples={
                        'invalid_token': {'error': 'Invalid Token'},
                        'token_required': {'error': 'Token is required'}
                    }
                )
            ),
        }
    )
    
    def post(self, request, *args, **kwargs):
        try:
            token = request.data.get("token")
            if not token:
                return self.bad_request_response("Token is required")
            
            # Attempt to decode the token
            _ = AccessToken(str(token))
            return self.success_response(message="Token is valid", data={"valid": True})
            
        except BadRequest:
            return self.bad_request_response("Invalid Token")


class GoogleLogin(SocialLoginView,ResponseMixin):

    adapter_class = GoogleOAuth2Adapter

    def post(self, request, *args, **kwargs):
        try:
            email = request.data.get("email")
            if not email:
                return Response({"error": "Invalid Email Address"}, status=status.HTTP_400_BAD_REQUEST)

            existing_user = ClinicUser.objects.filter(email=email).first()
            if existing_user and existing_user.auth_provider != "google":
                return Response({
                                    "error": "This email is already registered with a different method. Please log in using email and password."},
                                status=status.HTTP_400_BAD_REQUEST)

            _ = super().post(request, *args, **kwargs)
            user = self.user  # Ensure the authenticated user is available
            if not user:
                return self.internal_server_error_response("Oops, We were not able to log you in. Please try later")

            if not user.email_verified:
                user.auth_provider = "google"
                user.email_verified = True
                user.save()
                message = "User Registered Successfully"
            else:
                message = "Login successful"

            refresh = RefreshToken.for_user(user)
            jwt_tokens = {"access": str(refresh.access_token), "refresh": str(refresh)}

            doctor_data = {}
            if user.is_doctor:
                doctor = DoctorInformation.objects.filter(doctor=user).first()
                if doctor:
                    doctor_data = DoctorInformationSerializer(doctor).data

            response_data = {
                "auth_info": jwt_tokens,
                "user": LoginClinicUserSerializer(user).data,
                "doctor_data": doctor_data
            }

            return self.success_response(data=response_data, message=message)
        except Exception as e:
            raise e  # Consider logging instead of re-raising for better error handling



class RetrieveCalenderDataView(APIView):
    permission_classes = [IsAuthenticated,IsSuperUserOnly | IsAdminOnly | IsDoctorOnly]
    def get(self, request, *args, **kwargs):
        user = request.user
        access_token = get_google_access_token(user)
        if not access_token:
            return Response({"error": "No access token found"}, status=400)

        start_date = request.GET.get("start_date")
        end_date = request.GET.get("end_date")
        max_results = request.GET.get("max_results", 10)
        if not start_date or not end_date:
            return Response({"error": "start_date and end_date are required"}, status=400)

        try:
            start_date = datetime.datetime.fromisoformat(start_date).isoformat() + "Z"
            end_date = datetime.datetime.fromisoformat(end_date).isoformat() + "Z"
        except ValueError:
            return Response({"error": "Invalid date format. Use ISO 8601 format (YYYY-MM-DDTHH:MM:SS)"}, status=400)
        response = get_google_calender_events_by_date_range(access_token,start_date,end_date,max_results)

        if response.status_code == 200:
            return Response(response.json())

        return Response({"error": "Failed to fetch events", "details": response.json()}, status=response.status_code)


class ForgotPasswordView(ResponseMixin, APIView):
    permission_classes = [AllowAny]

    @swagger_auto_schema(
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'email': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_EMAIL, description='User email'),
            },
            required=['email']
        ),
        responses={
            200: openapi.Response('Password reset link sent successfully.'),
            400: openapi.Response('Email is required.'),
            404: openapi.Response('Email not found.'),
        }
    )
    def post(self, request):
        email = request.data.get("email")
        if not email:
            return self.bad_request_response("Email is required.")

        user = User.objects.filter(email=email).first()
        if not user:
            return self.bad_request_response("Email not found.")

        # Generate reset token
        token = jwt.encode(
            {
                "password":user.password,
                "email": user.email,
                "exp": datetime.datetime.now(datetime.UTC) + datetime.timedelta(hours=1)
            },
            settings.SECRET_KEY,
            algorithm="HS256"
        )

        reset_link = f"{settings.FRONTEND_URL}/reset-password?token={token}"
        message = password_reset.replace("<<reset_link>>", reset_link)

        # Send reset email
        send_mail(
            subject="Reset your password",
            message="",
            html_message=message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[email],
        )

        return self.success_response(message="Password reset link sent successfully.")


class ChangePasswordWithTokenView(ResponseMixin, APIView):
    permission_classes = [AllowAny]

    @swagger_auto_schema(
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'token': openapi.Schema(type=openapi.TYPE_STRING, description='Reset token'),
                'new_password': openapi.Schema(type=openapi.TYPE_STRING, description='New password to be set'),
                'confirm_password': openapi.Schema(type=openapi.TYPE_STRING, description='Confirmation of the new password'),
            },
            required=['token', 'new_password', 'confirm_password']
        ),
        responses={
            200: openapi.Response('Password changed successfully.'),
            400: openapi.Response('Bad request.'),
            404: openapi.Response('Invalid token.'),
        }
    )
    def post(self, request):
        token = request.data.get("token")
        new_password = request.data.get("new_password")
        confirm_password = request.data.get("confirm_password")

        if not token:
            return self.bad_request_response("Token is required.")
        if new_password != confirm_password:
            return self.bad_request_response("New passwords do not match.")

        try:
            payload = jwt.decode(token, settings.SECRET_KEY, algorithms=["HS256"])
            email = payload["email"]
            password = payload["password"]
            user = User.objects.get(email=email)
            if user.password != password:
                return self.bad_request_response("Password reset link has been expired!!")
            user.set_password(new_password)
            user.save()

            return self.success_response(message="Password changed successfully.")
        except jwt.ExpiredSignatureError:
            return self.bad_request_response("Reset token has expired.")
        except jwt.InvalidTokenError:
            return self.bad_request_response("Invalid token.")
        except User.DoesNotExist:
            return self.bad_request_response("User not found.")


class ResetPasswordForLoggedInUserView(ResponseMixin, APIView):
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'old_password': openapi.Schema(type=openapi.TYPE_STRING, description='Current password'),
                'new_password': openapi.Schema(type=openapi.TYPE_STRING, description='New password to be set'),
                'confirm_password': openapi.Schema(type=openapi.TYPE_STRING, description='Confirmation of the new password'),
            },
            required=['old_password', 'new_password', 'confirm_password']
        ),
        responses={
            200: openapi.Response('Password reset successfully.'),
            400: openapi.Response('Bad request.'),
            401: openapi.Response('Old password is incorrect.'),
        }
    )
    def post(self, request):
        user = request.user
        old_password = request.data.get("old_password")
        new_password = request.data.get("new_password")
        confirm_password = request.data.get("confirm_password")

        if not user.check_password(old_password):
            return self.bad_request_response("Old password is incorrect.")

        if new_password != confirm_password:
            return self.bad_request_response("New passwords do not match.")
        if new_password == old_password:
            return self.bad_request_response("New password should be different than old password")
        user.set_password(new_password)
        user.save()

        return self.success_response(message="Password reset successfully.")