from django.urls import path,include
from .views import RetrieveUpdateDeleteOrganizationView, \
    OrganizationCreationPipeLine, LoginViewV2, RefreshTokenView, LogoutViewV2, SignupView, EmailVerifyView, \
    ResendEmailVIew, RetrieveUpdateDeleteUserView, ListOrganizationUsers, S3BucketCreateView, LogGroupCreateView, \
    HealthCheckView, VerifyTokenView, GoogleLogin, RetrieveCalenderDataView, ForgotPasswordView, \
    ChangePasswordWithTokenView, ResetPasswordForLoggedInUserView

app_name = 'accounts'

urlpatterns = [
    path('auth/signup/', SignupView.as_view(), name='signup'),
    path('auth/verify-email/', EmailVerifyView.as_view(), name='email_verify'),
    path('auth/resend_verification_email/', ResendEmailVIew.as_view(), name='email_verify_resend'),
    path('auth/login/', LoginViewV2.as_view(), name='token_obtain_pair'),
    path('auth/refresh/', RefreshTokenView.as_view(), name='token_refresh'),
    path("auth/verify-token/",VerifyTokenView.as_view(),name="verify-token"),
    path('auth/logout/', LogoutViewV2.as_view(), name='token_logout'),
    path('auth/google/', GoogleLogin.as_view(), name='google_login'),
    path('auth/forgot_password_request/', ForgotPasswordView.as_view(), name='forgot-password-email'),
    path('auth/reset_password_with_token/', ChangePasswordWithTokenView.as_view(), name='password-reset-with-token'),
    path('auth/reset_password/', ResetPasswordForLoggedInUserView.as_view(), name='reset-password'),
    path('google_calender/', RetrieveCalenderDataView.as_view(), name='google_calender'),
    path("user/<int:pk>/",RetrieveUpdateDeleteUserView.as_view(),name="retrieve-update-delete-users"),
    path("org_users/<int:org>/",ListOrganizationUsers.as_view(),name="list-organizations"),
    path("organization/", OrganizationCreationPipeLine.as_view(), name="create-list-organization"),
    path("organization/<int:pk>/",RetrieveUpdateDeleteOrganizationView.as_view(),name="get-update-delete-organization"),
    path("bucket/",S3BucketCreateView.as_view(),name="create-s3-bucket-organizations"),
    path("log_group/",LogGroupCreateView.as_view(),name="create-log-group-organizations"),
    path("health_check/",HealthCheckView.as_view(),name="health-check"),

]


#User nAME NOT IN UI