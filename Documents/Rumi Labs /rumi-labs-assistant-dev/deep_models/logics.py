import httpx
from deepgram import DeepgramClient, PrerecordedOptions
import os
from dotenv import load_dotenv
from deep_models.gpt_models.aws_bedrock.llama import BedrockLLAMA
from deep_models.helpers.template_manager import TemplateManager
from string import Template
from deep_models.services.llm_service_factory import LLMServiceFactory
from deep_models.services.service_heartbeat_checker import HeartbeatCheckerService

load_dotenv()
deep_gram_key = os.getenv('DEEPGRAM_API_KEY')

tempalte_manager = TemplateManager()

def load_template_on_llm(template_name, param=None):
    heartbeat_checker = HeartbeatCheckerService()
    factory = LLMServiceFactory(heartbeat_checker)
    service =  factory.get_service()

    template = tempalte_manager.load(template_name)
    if param:
        template = Template(template)
        template = template.substitute(**param)
    notes = service.chat(template)
    return notes

