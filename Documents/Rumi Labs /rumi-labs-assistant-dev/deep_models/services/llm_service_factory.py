from deep_models.gpt_models.aws_bedrock.anthropic import BedrockAnthropic
from deep_models.gpt_models.aws_bedrock.llama import BedrockLLAMA
from deep_models.gpt_models.chat_gpt import ChatGPTLLM

class LLMServiceFactory:
    def __init__(self, heartbeat_checker):
        self.heartbeat_checker = heartbeat_checker

    def get_service(self):
        try:
            # cache = self.heartbeat_checker.heartbeat_cache
            # if cache.get("ChatGPT"):
            #     return ChatGPTLLM()
            return BedrockAnthropic()
        except Exception as e:
            raise AssertionError("No available LLM services",e)
