import time
import threading

from deep_models.gpt_models.aws_bedrock.llama import BedrockLLAM<PERSON>
from deep_models.gpt_models.chat_gpt import ChatGPTLLM
import uuid

class HeartbeatCheckerService:
    _instance = None
    _lock = threading.Lock()

    def __new__(cls, *args, **kwargs):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(HeartbeatCheckerService, cls).__new__(cls, *args, **kwargs)
                cls._instance._initialize()
        return cls._instance

    def _initialize(self):
        self.id = uuid.uuid4()
        self.interval = 50
        self.services = {
            "ChatGPT": ChatGPTLLM(),
            "BedrockLLAMA": BedrockLLAMA()
            # "AzureChatGPT": AzureChatGPT(),
            # "Anthropic": AnthropicLLM()
        }
        self.heartbeat_cache = {}
        self.start_heartbeat_checker()

    def start_heartbeat_checker(self):
        self.thread = threading.Thread(target=self.check_services)
        self.thread.daemon = True
        self.thread.start()

    def check_services(self):
        while True:
            try:
                for name, service in self.services.items():
                    is_available = service.check_availability()
                    self.heartbeat_cache[name] = is_available
                time.sleep(self.interval)
            except Exception as e:
                print(f"{e} {self.id}")
    
