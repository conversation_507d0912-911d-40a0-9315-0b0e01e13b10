from typing import Tu<PERSON>, List, Dict, BinaryIO
from .BaseTranscribe import BaseTranscribe
from django.conf import settings
from deepgram import DeepgramClient, PrerecordedOptions
import httpx
import logging

logger = logging.getLogger(__name__)

class DeepGram(BaseTranscribe):
    """DeepGram transcription service implementation."""
    
    TIMEOUT_SECONDS = 300.0
    CONNECT_TIMEOUT = 10.0
    API_VERSION = "1"
    MODEL_VERSION = "nova-3-medical"
    
    def __init__(self):
        """Initialize DeepGram client."""
        self.client = DeepgramClient(settings.DEEPGRAM_API_KEY)

    def transcribe(self, file: BinaryIO) -> Tuple[List[Dict], str]:
        """
        Transcribe audio file using DeepGram API with speaker diarization.
        
        Args:
            file: Audio file object to transcribe
            
        Returns:
            Tuple containing:
            - List of transcript segments with speaker information
            - Complete transcript text
            
        Raises:
            ValueError: If file is invalid
            DeepGramError: If transcription fails
        """
        try:
            if not file:
                raise ValueError("No file provided for transcription")

            response = self._get_transcription(file)
            return self._process_transcription(response)
            
        except Exception as e:
            logger.error(f"Transcription failed: {str(e)}")
            raise

    def _get_transcription(self, file: BinaryIO) -> Dict:
        """Get raw transcription from DeepGram API."""
        options = PrerecordedOptions(
            smart_format=True,
            model=self.MODEL_VERSION,
            diarize=True
        )
        
        timeout = httpx.Timeout(self.TIMEOUT_SECONDS, connect=self.CONNECT_TIMEOUT)
        
        response = self.client.listen.rest.v(self.API_VERSION).transcribe_file(
            {"buffer": file.read()},
            options,
            timeout=timeout
        )
        
        return response.to_dict()

    def _process_transcription(self,deepgram_response):
        """
        Converts Deepgram's transcript response to the desired format.

        Args:
            deepgram_response (dict): The JSON response from Deepgram API.

        Returns:
            list: A list of dictionaries with 'start', 'end', 'speaker', and 'text' keys.
        """
        output = []
        transcript_txt = ""
        try:
            words = deepgram_response["results"]["channels"][0]["alternatives"][0]["words"]
            current_speaker = None
            current_segment = {
                "start": None,
                "end": None,
                "speaker": None,
                "text": ""
            }

            for word in words:
                speaker = word.get("speaker", 0)

                if speaker != current_speaker:
                    if current_segment["text"]:
                        output.append(current_segment)
                        transcript_txt += f"\n{current_segment['text']}"
                    current_segment = {
                        "start": word["start"],
                        "end": word["end"],
                        "speaker": speaker,
                        "text": word["word"]
                    }
                    current_speaker = speaker
                else:
                    current_segment["end"] = word["end"]
                    current_segment["text"] += " " + word["word"]

            if current_segment["text"]:
                output.append(current_segment)
                transcript_txt += f"\n{current_segment['text']}"

        except (KeyError, IndexError):
            print("Invalid Deepgram response format")
        return output,transcript_txt

