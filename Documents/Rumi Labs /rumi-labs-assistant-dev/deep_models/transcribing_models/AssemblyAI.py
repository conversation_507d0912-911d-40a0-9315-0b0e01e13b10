import assemblyai as aai
from django.conf import settings
from .BaseTranscribe import BaseTranscribe

assembly_ai_api_key = settings.ASSEMBLY_AI_API_KEY
aai.settings.api_key = assembly_ai_api_key


class AssemblyAI(BaseTranscribe):
    def transcribe(self, file):
        config = aai.TranscriptionConfig(speaker_labels=False)

        transcriber = aai.Transcriber()
        transcript = transcriber.transcribe(
            file,
            config=config
        )
        return transcript.text
