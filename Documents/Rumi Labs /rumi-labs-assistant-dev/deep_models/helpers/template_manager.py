import ast


class TemplateManager:
    def __init__(self) -> None:
        file_path = "deep_models/helpers/templates.py"
        self.templates = self._load_templates(file_path)

    def load(self,template_name):
        return self.templates[template_name]

        
    def _load_templates(self,file_path):
            # Read the source code from file
        strings_dict = {}
        with open(file_path, 'r') as f:
            # Read the entire content of the file
            file_content = f.read()
            
            # Use exec to execute the file content in a controlled environment
            exec_globals = {}
            exec(file_content, exec_globals)
            
            # Filter out only string variables
            string_vars = {var_name: value for var_name, value in exec_globals.items()
                        if isinstance(value, str)}
            
            # Update the dictionary
            strings_dict.update(string_vars)
        
        return strings_dict

