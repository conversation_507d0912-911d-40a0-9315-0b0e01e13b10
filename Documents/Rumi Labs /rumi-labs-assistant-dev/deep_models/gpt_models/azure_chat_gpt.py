import os
import openai
from deep_models.gpt_models.chat_gpt import ChatGPTLLM
import requests
azure_openai_key = os.getenv("azure_openai_key")

class AzureChatGPT(ChatGPTLLM):
    def __init__(self, stream = True):
        super(AzureChatGPT, self).__init__(stream)
        self._set_credentials()

    def _set_credentials(self):
        openai.api_type = "azure"
        openai.api_base = ""
        openai.api_version = ""
        openai.api_key = azure_openai_key

    def _generate_result_and_stream(self):
        response = openai.ChatCompletion.create(
            engine="GPT35",
            messages=self.messages,
            temperature=0.7,
            max_tokens=4000,
            top_p=0.95,
            frequency_penalty=0,
            presence_penalty=0,
            stop=None,
            stream=True
        )
        answer = ""
        for chunk in response:
            data = chunk["choices"][0]["delta"].get("content")
            if data is not None:
                answer += str(data)
                self.broadcast_reponse_to_channel(data)
        return answer

    def _generate_non_stream_result(self):
        response = openai.ChatCompletion.create(
            engine="GPT35",
            messages=self.messages,
            temperature=0.7,
            max_tokens=4000,
            top_p=0.95,
            frequency_penalty=0,
            presence_penalty=0,
            stop=None)
        return response["choices"][0]["message"].get("content")
    
    def check_availability(self):
        try:
            response = requests.get("https://status.azure.com/en-us/status")
            response.raise_for_status()
            return "Operational" in response.text
        except requests.RequestException as e:
            print(f"Failed to check Azure status: {e}")
            return False