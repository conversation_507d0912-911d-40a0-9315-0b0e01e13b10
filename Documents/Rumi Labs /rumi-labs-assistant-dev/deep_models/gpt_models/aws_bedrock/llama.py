from project_helper.aws_helper import <PERSON><PERSON><PERSON><PERSON><PERSON>
from deep_models.gpt_models.base_llm import BaseLLM
import json
from django.conf import settings

region_name = settings.BEDROCK_REGION_NAME


class BedrockLLAMA(BaseLLM):
    def __init__(self,stream = False):
        super().__init__(stream)
        aws_helper = AWSHelper(region_name=region_name)
        self.client = aws_helper.bedrock_client
        self.model_id = "meta.llama3-1-8b-instruct-v1:0"

    def _generate_result_and_stream(self):
        max_gen_len = 2048
        temperature = 0.7
        top_p = 0.9

        # Create request body.
        body = json.dumps({
            "prompt": self.messages,
            "max_gen_len": max_gen_len,
            "temperature": temperature,
            "top_p": top_p
        })
        streaming_response = self.client.invoke_model_with_response_stream(
            modelId=self.model_id, body=body
        )

        # Extract and print the response text in real-time.
        for event in streaming_response["body"]:
            chunk = json.loads(event["chunk"]["bytes"])
            if "generation" in chunk:
                print(chunk["generation"], end="")


    def _generate_non_stream_result(self):
        max_gen_len = 2048
        temperature = 0.7
        top_p = 0.9

        # Create request body.
        body = json.dumps({
            "prompt": self.messages,
            "max_gen_len": max_gen_len,
            "temperature": temperature,
            "top_p": top_p
        })

        response = self.client.invoke_model(
            body=body, modelId=self.model_id)

        response_body = json.loads(response.get('body').read())
        return response_body['generation']
    def chat(self, message):
        self.messages = f"""
            <|begin_of_text|>
            <|start_header_id|>user<|end_header_id|>
            {message}
            <|eot_id|>
            <|start_header_id|>assistant<|end_header_id|>
            """
        if self.stream:
            answer = self._generate_result_and_stream()
        else:
            answer = self._generate_non_stream_result()
        return "Data Sent Successfully" if self.stream else answer


    def check_availability(self):
        # no definite method found
        pass

if __name__=="__main__":
    llm = BedrockLLAMA(False)
    print(llm.chat("Hello There"))