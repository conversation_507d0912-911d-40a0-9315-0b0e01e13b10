from project_helper.aws_helper import <PERSON><PERSON><PERSON><PERSON><PERSON>
from deep_models.gpt_models.base_llm import BaseLLM
import json
from django.conf import settings

region_name = "us-east-1"


class BedrockAnthropic(BaseLLM):
    def __init__(self,stream = False):
        super().__init__(stream)
        aws_helper = AWSHelper(region_name=region_name)
        self.client = aws_helper.bedrock_client
        # self.model_id = "meta.llama3-1-8b-instruct-v1:0"
        self.model_id = "us.anthropic.claude-3-5-haiku-20241022-v1:0"

    def _generate_result_and_stream(self):
        return ""


    def _generate_non_stream_result(self):

        response = self.client.converse(
            modelId=self.model_id,
            messages=self.messages,
        )
        return response["output"]["message"]["content"][0]["text"]
    def chat(self, message):
        self.messages = [
            {
                "role": "user",
                "content": [{"text": message}],
            }
        ]
        if self.stream:
            answer = self._generate_result_and_stream()
        else:
            answer = self._generate_non_stream_result()
        return "Data Sent Successfully" if self.stream else answer
    def check_availability(self):
        # no definite method found
        pass

if __name__=="__main__":
    llm = BedrockAnthropic(False)
    print(llm.chat("Hello There"))