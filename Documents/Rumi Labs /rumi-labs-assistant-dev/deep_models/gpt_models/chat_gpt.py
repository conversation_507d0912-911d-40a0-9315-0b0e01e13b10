import os
from openai import OpenAI
from deep_models.gpt_models.base_llm import BaseLLM
from dotenv import load_dotenv
import requests

load_dotenv()
api_key = os.getenv("OPENAI_API_KEY")

def get_api_key():
    return api_key


class ChatGPTLLM(BaseLLM):
    def __init__(self, stream = False):
        super().__init__(stream)
        self.client = OpenAI(api_key=api_key,)

    def _set_credentials(self):
        self.client.api_key = api_key

    def _generate_result_and_stream(self):
        response = self.client.ChatCompletion.create(
            model="gpt-4",
            messages=self.messages,
            temperature=0,
            stream=True
        )

        answer = ""
        for chunk in response:
            data = chunk["choices"][0]["delta"].get("content")
            if data is not None:
                answer += str(data)
                self.broadcast_reponse_to_channel(data)
        return answer

    def _generate_non_stream_result(self):

        response = self.client.chat.completions.create(
            messages=self.messages,
            model="gpt-4o-mini",
        )
        response =response.to_dict()
        return response["choices"][0]["message"].get("content")

    def chat(self, message):
        self._set_credentials()
        self.messages = [{"role": "user", "content": message}]
        if self.stream:
            answer = self._generate_result_and_stream()
        else:
            answer = self._generate_non_stream_result()
        return "Data Sent Successfully" if self.stream else answer
    
    def check_availability(self):
        try:
            response = requests.get("https://status.openai.com/api/v2/status.json")
            response.raise_for_status()
            status = response.json()
            return status.get("status", {}).get("description") == "All Systems Operational"
        except requests.RequestException as e:
            print(f"Failed to check OpenAI status: {e}")
            return False
