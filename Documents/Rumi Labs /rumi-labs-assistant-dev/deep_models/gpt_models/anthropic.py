import os
import anthropic
from deep_models.gpt_models.base_llm import BaseLLM
from dotenv import load_dotenv
import requests


load_dotenv()
api_key = os.getenv("anthropic_key")
def get_api_key():
    return api_key


class AnthropicLLM(BaseLLM):
    def __init__(self, stream = True):
        super().__init__(stream)
        self.client = anthropic.Anthropic(api_key=api_key)


    def _generate_result_and_stream(self):
        answer = ""
        with self.client.messages.stream(
                max_tokens=4000,
                messages=[{"role": "user", "content":self.messages}],
                model="claude-3-5-sonnet-20240620",
        ) as stream:
            for text in stream.text_stream:
                if text is not None:
                    answer += str(text)
                    self.broadcast_reponse_to_channel(text)
        return answer

    def _generate_non_stream_result(self):
        response = self.client.messages.create(
            model="claude-3-5-sonnet-20240620",
            max_tokens=4000,
            messages=[{"role": "user", "content": self.messages}]
        )

        return response["choices"][0]["message"].get("content")

    def chat(self, message):
        self.messages = [{"role": "user", "content": message}]
        if self.stream:
            answer = self._generate_result_and_stream()
        else:
            answer = self._generate_non_stream_result()
        return "Data Sent Successfully" if self.stream else answer
    
    def check_availability(self):
        try:
            response = requests.get("https://status.anthropic.com")
            response.raise_for_status()
            return "Operational" in response.text
        except requests.RequestException as e:
            print(f"Failed to check Anthropic status: {e}")
            return False
