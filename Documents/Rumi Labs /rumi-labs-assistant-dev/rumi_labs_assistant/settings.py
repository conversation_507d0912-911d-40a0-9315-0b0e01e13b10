"""
Django settings for rumi_labs_assistant project.

Generated by 'django-admin startproject' using Django 5.0.7.

For more information on this file, see
https://docs.djangoproject.com/en/5.0/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.0/ref/settings/
"""
from datetime import timed<PERSON><PERSON>
from pathlib import Path
import os
from dotenv import load_dotenv
from corsheaders.defaults import default_headers
import sentry_sdk

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.0/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = os.getenv("DJANGO_SECRET_KEY")

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

## Environment Variables ##

load_dotenv()
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
DEEPGRAM_API_KEY = os.getenv("DEEPGRAM_API_KEY")
AWS_SERVER_PUBLIC_KEY = os.getenv("AWS_SERVER_PUBLIC_KEY")
AWS_SERVER_SECRET_KEY = os.getenv("AWS_SERVER_SECRET_KEY")
AWS_REGION_NAME = os.getenv("AWS_REGION_NAME")
BEDROCK_REGION_NAME = os.getenv("BEDROCK_REGION_NAME")
CLOUDWATCH_GROUP_NAME = os.getenv("CLOUDWATCH_GROUP_NAME")
CLOUDWATCH_STREAM_NAME = os.getenv("CLOUDWATCH_STREAM_NAME")
COGNITO_USER_POOL_ID = os.getenv("COGNITO_USER_POOL_ID")
COGNITO_CLIENT_ID = os.getenv("COGNITO_CLIENT_ID")
LOGGER_TYPE = os.getenv("LOGGER_TYPE")
ASSEMBLY_AI_API_KEY = os.getenv("ASSEMBLY_AI_API_KEY")


DB_NAME = os.getenv("DB_NAME")
DB_USER = os.getenv("DB_USER")
DB_HOST = os.getenv("DB_HOST")
DB_PASSWORD = os.getenv("DB_PASSWORD")
GOOGLE_CLIENT_ID = os.getenv("GOOGLE_CLIENT_ID")
GOOGLE_SECRET = os.getenv("GOOGLE_SECRET")
#############################################

ACCESS_TOKEN_LIFETIME_MINUTES = 60
REFRESH_TOKEN_LIFETIME_DAYS = 1


import sentry_sdk

sentry_sdk.init(
    dsn="https://<EMAIL>/4508880339009536",
    # Add data like request headers and IP for users,
    # see https://docs.sentry.io/platforms/python/data-management/data-collected/ for more info
    send_default_pii=True,
)

CSRF_TRUSTED_ORIGINS = ["https://dev-api.rumilabs.ai"]


ALLOWED_HOSTS = ["127.0.0.1","localhost","localhost:9653",
                 "product.rumilabs.ai","dm-api.rumilabs.ai","api.rumilabs.ai",
                 "dev-api.rumilabs.ai","dev-dm-api.rumilabs.ai","dev-frontend.rumilabs.ai","**********/16"]

SWAGGER_SETTINGS = {
    'SECURITY_DEFINITIONS': {
        'Bearer': {
            'type': 'apiKey',
            'name': 'Authorization',
            'in': 'header'
        }
    },
    'USE_SESSION_AUTH': False,
}

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'rest_framework',
    'corsheaders',
    'rest_framework_simplejwt',
    'dj_rest_auth',
    'rest_framework.authtoken',
    'rest_framework_simplejwt.token_blacklist',
    'accounts',
    'health_care',
    'deep_models',
    'log_and_monitor',
    'drf_yasg',
    "django.contrib.sites",
    "allauth",
    "allauth.account",
    "allauth.socialaccount",
    "allauth.socialaccount.providers.google",
    "dj_rest_auth.registration",
]

AUTHENTICATION_BACKENDS = (
    "allauth.account.auth_backends.AuthenticationBackend",
    'django.contrib.auth.backends.ModelBackend',
)

REST_USE_JWT = True

SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(minutes=ACCESS_TOKEN_LIFETIME_MINUTES),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=REFRESH_TOKEN_LIFETIME_DAYS),
    'ROTATE_REFRESH_TOKENS': True,
    'BLACKLIST_AFTER_ROTATION': True,
}

FRONTEND_URL= os.getenv("FRONTEND_URL")
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.gmail.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = os.getenv("VERIFICATION_EMAIL")
EMAIL_HOST_PASSWORD = os.getenv("VERIFICATION_EMAIL_PASSWORD")
DEFAULT_FROM_EMAIL = os.getenv("VERIFICATION_EMAIL")


AUTH_USER_MODEL = 'accounts.ClinicUser'

ADMIN_USER_NAME='RumiLabs Admin'
ADMIN_USER_EMAIL= os.getenv("VERIFICATION_EMAIL")

SOCIALACCOUNT_STORE_TOKENS = True

SOCIALACCOUNT_PROVIDERS = {
   'google': {
       'APP': {
           'client_id': GOOGLE_CLIENT_ID,
           'secret': GOOGLE_SECRET,
           'key': ''
       },
        "SCOPE": ["email", "profile",'https://www.googleapis.com/auth/calendar.readonly',],
        "AUTH_PARAMS": {"access_type": "offline",
            "prompt": "consent",}
   }
}


REST_FRAMEWORK = {
    "DEFAULT_AUTHENTICATION_CLASSES": [
        "rest_framework_simplejwt.authentication.JWTAuthentication",
    ],
}
MIDDLEWARE = [
    'rumi_labs_assistant.middleware.HealthCheckHostBypassMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'rumi_labs_assistant.middleware.LoggingMiddleware',
    'allauth.account.middleware.AccountMiddleware'
]
CORS_ALLOW_ALL_ORIGINS = True
# CORS_ALLOWED_ORIGINS = [
#     "https://dev-frontend.rumilabs.ai",
#     "http://localhost",
#     "http://127.0.0.1:8000",
#     "http://localhost:9653",
#     "https://dev-deepgram-microservice.rumilabs.ai",
#     "https://dev-backend.rumilabs.ai"
# ]

ROOT_URLCONF = 'rumi_labs_assistant.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'rumi_labs_assistant.wsgi.application'
CORS_ALLOW_CREDENTIALS = True
CORS_ALLOW_HEADERS = list(default_headers)
CORS_ORIGIN_ALLOW_ALL = True
# Database
# https://docs.djangoproject.com/en/5.0/ref/settings/#databases

DATABASES = {
    'default':
    # {
    #     'ENGINE': 'django.db.backends.sqlite3',
    #     'NAME': BASE_DIR / 'db.sqlite3',
    # }
    {
        'ENGINE': 'django.db.backends.mysql',  # MySQL database engine
        'NAME': DB_NAME,  # Your database name
        'USER': DB_USER,  # Your database username
        'PASSWORD': DB_PASSWORD,  # Your database password
        'HOST': DB_HOST,  # Use '127.0.0.1' or remote host
        'PORT': '3306',  # Default MySQL port
        'OPTIONS': {
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'"
        }
    }
}


# Password validation
# https://docs.djangoproject.com/en/5.0/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# AllAuth settings
SITE_ID = int(os.getenv("SITE_ID")) # Required for django-allauth

REST_USE_JWT = True  # Ensure dj-rest-auth uses JWT

SOCIALACCOUNT_PROVIDERS = {
    'google': {
        'SCOPE': ['profile', 'email'],
        'AUTH_PARAMS': {'access_type': 'online'},
    }
}


# Internationalization
# https://docs.djangoproject.com/en/5.0/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.0/howto/static-files/

STATIC_URL = 'static/'

# Default primary key field type
# https://docs.djangoproject.com/en/5.0/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')
