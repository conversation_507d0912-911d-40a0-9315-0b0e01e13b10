"""
URL configuration for rumi_labs_assistant project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.0/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from project_helper.swagger import schema_view


urlpatterns = [
    path('admin/', admin.site.urls),
    path("accounts/", include("accounts.urls", "accounts-management")),
    path('auth/', include('dj_rest_auth.urls')),  # JWT Auth
    path('auth/registration/', include('dj_rest_auth.registration.urls')),  # Signup/Login
    path('auth/social/', include('allauth.socialaccount.urls')),
    path("health_care/", include("health_care.urls", "health-care")),
    path("log_and_monitor/", include("log_and_monitor.urls", "log-and-monitor")),
    path('swagger/', schema_view.with_ui('swagger', cache_timeout=0), name='schema-swagger-ui'),
    path('redoc/', schema_view.with_ui('redoc', cache_timeout=0), name='schema-redoc'),

]
