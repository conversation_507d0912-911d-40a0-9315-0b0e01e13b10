import datetime
from django.conf import settings
from django.utils.deprecation import MiddlewareMixin
from log_and_monitor.logger_classes.cloud_watch import CloudWatchLogger
from django.contrib.auth import get_user_model
from django.http import HttpResponse

import jwt

User = get_user_model()


class LoggingMiddleware(MiddlewareMixin):
    def __init__(self, get_response):
        self.get_response = get_response

        if settings.LOGGER_TYPE == 'cloudwatch':
            self.logger = CloudWatchLogger()
        else:
            raise ValueError("Invalid LOGGER_TYPE specified")


    def process_response(self, request, response):
        if "health_check" in request.path or "swagger" in request.path:
            return response
        try:
            user = request.user
            endpoint = request.path
            if "admin" in request.path:
                return response
            current_time = datetime.datetime.now().isoformat()
            if "login" in request.path:
                decoded_data = jwt.decode(response.data["access"], settings.SECRET_KEY, algorithms=["HS256"])
                user = User.objects.get(id=decoded_data.get("user_id"))
            elif "signup" in request.path or "logout" in request.path or "refresh" in request.path or "auth/google/" in request.path:
                user = None
            if user is None:
                self.logger.log(-1, "Anonymous", endpoint, current_time, str(response.status_code),"")
            else:
                self.logger.log(user.id, user.organization.name, endpoint, current_time,str(response.status_code),user.organization.log_group_name)
                print("Added Log to CW")


        except Exception as e:
            print("Exception in middleware",e)
        return response



class HealthCheckHostBypassMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response
        self.health_check_path = '/accounts/health_check/'  # change this to your actual health check path

    def __call__(self, request):
        if request.path == self.health_check_path and request.META['REMOTE_ADDR'].startswith('172.31.'):
            response = HttpResponse("OK")
            response['X-Bypass-Allowed-Hosts'] = 'true'
            return response
        return self.get_response(request)